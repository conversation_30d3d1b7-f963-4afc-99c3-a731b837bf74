"""
FastAPI服务器集成示例
展示如何将FastAPI服务器与现有的数据管理器和ZMQ系统集成
"""

import time
import threading
import logging
from typing import Optional
import asyncio

from .fastapi_server import start_server, set_data_manager, set_rpc_client
from .stock_rpc_service import StockDataRPCClient

logger = logging.getLogger(__name__)


class FastAPIIntegration:
    """FastAPI集成管理器"""
    
    def __init__(self, data_manager=None, rpc_client=None, 
                 api_host: str = "0.0.0.0", api_port: int = 8000):
        """
        初始化FastAPI集成
        
        Args:
            data_manager: 数据管理器实例
            rpc_client: RPC客户端实例
            api_host: API服务器主机
            api_port: API服务器端口
        """
        self.data_manager = data_manager
        self.rpc_client = rpc_client
        self.api_host = api_host
        self.api_port = api_port
        
        self.server_thread = None
        self.is_running = False
        
    def start_api_server(self):
        """启动API服务器"""
        if self.is_running:
            logger.warning("API服务器已经在运行")
            return
        
        def run_server():
            try:
                start_server(
                    host=self.api_host,
                    port=self.api_port,
                    data_manager_instance=self.data_manager,
                    rpc_client_instance=self.rpc_client
                )
            except Exception as e:
                logger.error(f"API服务器启动失败: {e}")
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        self.is_running = True
        
        logger.info(f"FastAPI服务器已启动: http://{self.api_host}:{self.api_port}")
        logger.info(f"API文档: http://{self.api_host}:{self.api_port}/docs")
    
    def stop_api_server(self):
        """停止API服务器"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.server_thread:
            self.server_thread.join(timeout=5)
        
        logger.info("FastAPI服务器已停止")


def example_with_mock_data():
    """使用模拟数据的示例"""
    print("=== FastAPI服务器 - 模拟数据示例 ===")
    
    # 创建模拟数据管理器
    class MockDataManager:
        def __init__(self):
            self.full_ticks = {}
            self.code_list = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"]
            self._generate_mock_data()
        
        def _generate_mock_data(self):
            import random
            for code in self.code_list:
                self.full_ticks[code] = {
                    "stock_code": code,
                    "timestamp": int(time.time() * 1000),
                    "last_price": round(10 + random.uniform(-2, 2), 2),
                    "volume": random.randint(1000, 100000),
                    "amount": random.randint(10000, 1000000),
                    "open": round(10 + random.uniform(-1, 1), 2),
                    "high": round(10 + random.uniform(0, 3), 2),
                    "low": round(10 + random.uniform(-3, 0), 2),
                    "pre_close": 10.0,
                    "bid1_price": round(10 + random.uniform(-2, 2), 2),
                    "bid1_vol": random.randint(100, 1000),
                    "ask1_price": round(10 + random.uniform(-2, 2), 2),
                    "ask1_vol": random.randint(100, 1000)
                }
        
        def update_data(self):
            """更新模拟数据"""
            import random
            for code in self.code_list:
                current_price = self.full_ticks[code]["last_price"]
                new_price = round(current_price * (1 + random.uniform(-0.02, 0.02)), 2)
                self.full_ticks[code]["last_price"] = new_price
                self.full_ticks[code]["timestamp"] = int(time.time() * 1000)
                self.full_ticks[code]["volume"] += random.randint(100, 1000)
    
    # 创建数据管理器
    data_manager = MockDataManager()
    
    # 创建FastAPI集成
    api_integration = FastAPIIntegration(
        data_manager=data_manager,
        api_host="0.0.0.0",
        api_port=8000
    )
    
    try:
        # 启动API服务器
        api_integration.start_api_server()
        
        print("FastAPI服务器已启动!")
        print("访问以下URL测试API:")
        print("- 主页: http://localhost:8000/")
        print("- API文档: http://localhost:8000/docs")
        print("- 健康检查: http://localhost:8000/health")
        print("- 最新tick数据: http://localhost:8000/api/v1/tick/latest")
        print("- 指定股票tick: http://localhost:8000/api/v1/tick/000001.SZ")
        print("- 分钟数据: http://localhost:8000/api/v1/minute/000001.SZ")
        print("- 日线数据: http://localhost:8000/api/v1/daily/000001.SZ")
        print("- 市场快照: http://localhost:8000/api/v1/market/snapshot")
        print("- 服务器统计: http://localhost:8000/api/v1/stats")
        
        # 定期更新数据
        def update_data_loop():
            while api_integration.is_running:
                data_manager.update_data()
                time.sleep(2)  # 每2秒更新一次数据
        
        update_thread = threading.Thread(target=update_data_loop, daemon=True)
        update_thread.start()
        
        # 保持服务运行
        print("\n按Ctrl+C停止服务...")
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n正在停止服务...")
    finally:
        api_integration.stop_api_server()


def example_with_zmq_integration():
    """与ZMQ系统集成的示例"""
    print("=== FastAPI服务器 - ZMQ集成示例 ===")
    
    from .stock_data_bridge import StockDataPublisher, StockDataSubscriber
    from .stock_rpc_service import StockDataRPCService, StockDataRPCClient
    
    # 创建模拟数据管理器
    class IntegratedDataManager:
        def __init__(self):
            self.full_ticks = {}
            self.code_list = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH", "000858.SZ"]
            
        def update_tick(self, tick_data):
            """更新tick数据"""
            self.full_ticks[tick_data["stock_code"]] = tick_data
    
    data_manager = IntegratedDataManager()
    
    # 启动ZMQ发布订阅系统
    publisher = StockDataPublisher(port=5555)
    publisher.start()
    
    subscriber = StockDataSubscriber(port=5555)
    subscriber.start()
    
    # 订阅数据并更新到数据管理器
    def on_tick_update(tick_data):
        tick_dict = {
            "stock_code": tick_data.stock_code,
            "timestamp": tick_data.timestamp,
            "last_price": tick_data.last_price,
            "volume": tick_data.volume,
            "amount": tick_data.amount,
            "open": tick_data.open,
            "high": tick_data.high,
            "low": tick_data.low,
            "pre_close": tick_data.pre_close,
            "bid1_price": tick_data.bid1_price,
            "bid1_vol": tick_data.bid1_vol,
            "ask1_price": tick_data.ask1_price,
            "ask1_vol": tick_data.ask1_vol
        }
        data_manager.update_tick(tick_dict)
    
    subscriber.subscribe_all_stocks(on_tick_update)
    
    # 启动RPC服务
    rpc_service = StockDataRPCService(port=5556, data_manager=data_manager)
    rpc_service.start()
    
    # 创建RPC客户端
    rpc_client = StockDataRPCClient(port=5556)
    rpc_client.connect()
    
    # 创建FastAPI集成
    api_integration = FastAPIIntegration(
        data_manager=data_manager,
        rpc_client=rpc_client,
        api_host="0.0.0.0",
        api_port=8001  # 使用不同端口避免冲突
    )
    
    try:
        # 启动API服务器
        api_integration.start_api_server()
        
        print("完整集成系统已启动!")
        print("- ZMQ发布订阅: 端口5555")
        print("- ZMQ RPC服务: 端口5556") 
        print("- FastAPI服务: http://localhost:8001")
        print("- API文档: http://localhost:8001/docs")
        
        # 模拟数据生成
        def generate_data():
            import random
            for i in range(100):
                for stock_code in data_manager.code_list:
                    tick_data = {
                        "stock_code": stock_code,
                        "timestamp": int(time.time() * 1000),
                        "last_price": round(10 + random.uniform(-2, 2), 2),
                        "volume": random.randint(100, 10000),
                        "amount": random.randint(1000, 100000),
                        "open": round(10 + random.uniform(-1, 1), 2),
                        "high": round(10 + random.uniform(0, 3), 2),
                        "low": round(10 + random.uniform(-3, 0), 2),
                        "pre_close": 10.0,
                        "bid1_price": round(10 + random.uniform(-2, 2), 2),
                        "bid1_vol": random.randint(100, 1000),
                        "ask1_price": round(10 + random.uniform(-2, 2), 2),
                        "ask1_vol": random.randint(100, 1000)
                    }
                    publisher.publish_tick(tick_data)
                
                time.sleep(1)
        
        # 启动数据生成线程
        data_thread = threading.Thread(target=generate_data, daemon=True)
        data_thread.start()
        
        print("\n系统运行中，按Ctrl+C停止...")
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n正在停止系统...")
    finally:
        api_integration.stop_api_server()
        rpc_client.disconnect()
        rpc_service.stop()
        subscriber.stop()
        publisher.stop()


def example_standalone_api():
    """独立运行FastAPI服务器"""
    print("=== 独立FastAPI服务器示例 ===")
    
    # 直接启动FastAPI服务器（使用模拟数据）
    try:
        print("启动独立FastAPI服务器...")
        print("访问 http://localhost:8002/docs 查看API文档")
        
        start_server(host="0.0.0.0", port=8002)
        
    except KeyboardInterrupt:
        print("服务器已停止")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("python fastapi_integration.py mock       # 使用模拟数据")
        print("python fastapi_integration.py zmq        # 与ZMQ系统集成")
        print("python fastapi_integration.py standalone # 独立运行API服务器")
        sys.exit(1)
    
    mode = sys.argv[1]
    
    if mode == "mock":
        example_with_mock_data()
    elif mode == "zmq":
        example_with_zmq_integration()
    elif mode == "standalone":
        example_standalone_api()
    else:
        print(f"未知模式: {mode}")
        sys.exit(1)
