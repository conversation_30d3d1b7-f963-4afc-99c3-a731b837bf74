"""
股票数据桥接模块
整合ZMQ发布订阅和RPC，专为股票tick数据传输优化
提供高级封装和便捷接口
"""

import time
import logging
import threading
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from queue import Queue, Empty
import pandas as pd

from .zmq_pubsub import ZMQPublisher, ZMQSubscriber
from .zmq_rpc import ZMQRPCServer, ZMQRPCClient

logger = logging.getLogger(__name__)


@dataclass
class StockTickData:
    """标准化的股票tick数据结构"""
    stock_code: str
    timestamp: int
    last_price: float
    volume: int
    amount: float
    bid1_price: float = 0.0
    bid1_vol: int = 0
    ask1_price: float = 0.0
    ask1_vol: int = 0
    open: float = 0.0
    high: float = 0.0
    low: float = 0.0
    pre_close: float = 0.0
    
    # 五档行情
    bid_prices: List[float] = None
    bid_volumes: List[int] = None
    ask_prices: List[float] = None
    ask_volumes: List[int] = None
    
    def __post_init__(self):
        if self.bid_prices is None:
            self.bid_prices = [0.0] * 5
        if self.bid_volumes is None:
            self.bid_volumes = [0] * 5
        if self.ask_prices is None:
            self.ask_prices = [0.0] * 5
        if self.ask_volumes is None:
            self.ask_volumes = [0] * 5


class StockDataPublisher:
    """
    股票数据发布者
    高性能发布股票tick数据
    """
    
    def __init__(self, port: int = 5555, serialization: str = "msgpack"):
        """
        初始化股票数据发布者
        
        Args:
            port: 发布端口
            serialization: 序列化方式
        """
        self.publisher = ZMQPublisher(port=port, serialization=serialization, hwm=50000)
        self.is_running = False
        
        # 数据缓存和批处理
        self.tick_buffer: Dict[str, StockTickData] = {}
        self.buffer_size = 100
        self.flush_interval = 0.1  # 100ms
        self.last_flush_time = time.time()
        
        # 性能优化
        self.batch_mode = True
        self.compression_enabled = False
        
    def start(self):
        """启动发布者"""
        self.publisher.start()
        self.is_running = True
        logger.info("Stock Data Publisher started")
    
    def stop(self):
        """停止发布者"""
        self.is_running = False
        self.publisher.stop()
        logger.info("Stock Data Publisher stopped")
    
    def publish_tick(self, tick_data: Union[StockTickData, Dict[str, Any]]):
        """
        发布单个tick数据
        
        Args:
            tick_data: tick数据
        """
        if not self.is_running:
            return False
        
        # 转换为标准格式
        if isinstance(tick_data, dict):
            tick_data = self._dict_to_tick(tick_data)
        
        if self.batch_mode:
            # 批处理模式
            self.tick_buffer[tick_data.stock_code] = tick_data
            
            # 检查是否需要刷新
            current_time = time.time()
            if (len(self.tick_buffer) >= self.buffer_size or 
                current_time - self.last_flush_time >= self.flush_interval):
                self._flush_buffer()
                
        else:
            # 立即发送
            topic = f"tick.{tick_data.stock_code}"
            return self.publisher.publish(topic, asdict(tick_data))
    
    def publish_tick_batch(self, tick_list: List[Union[StockTickData, Dict[str, Any]]]):
        """
        批量发布tick数据
        
        Args:
            tick_list: tick数据列表
        """
        if not self.is_running:
            return False
        
        success_count = 0
        for tick_data in tick_list:
            if isinstance(tick_data, dict):
                tick_data = self._dict_to_tick(tick_data)
            
            topic = f"tick.{tick_data.stock_code}"
            if self.publisher.publish(topic, asdict(tick_data)):
                success_count += 1
        
        return success_count == len(tick_list)
    
    def publish_market_snapshot(self, market_data: Dict[str, Any]):
        """
        发布市场快照

        Args:
            market_data: 市场数据字典 {stock_code: tick_data}
        """
        if not self.is_running:
            return False

        # 转换数据格式
        formatted_data = {}
        for stock_code, tick_data in market_data.items():
            if isinstance(tick_data, dict):
                tick_data = self._dict_to_tick(tick_data)
            formatted_data[stock_code] = asdict(tick_data)

        # 发布批量数据（新的主要方式）
        success = self.publisher.publish_tick_batch(formatted_data)

        # 发布市场快照主题（保持兼容性）
        self.publisher.publish("market.snapshot", formatted_data)

        return success
    
    def _dict_to_tick(self, data: Dict[str, Any]) -> StockTickData:
        """将字典转换为StockTickData"""
        # 兼容现有数据格式
        return StockTickData(
            stock_code=data.get("stock_code", ""),
            timestamp=data.get("timestamp", int(time.time() * 1000)),
            last_price=data.get("last_price", 0.0),
            volume=data.get("volume", 0),
            amount=data.get("amount", 0.0),
            bid1_price=data.get("bid1_price", 0.0),
            bid1_vol=data.get("bid1_vol", 0),
            ask1_price=data.get("ask1_price", 0.0),
            ask1_vol=data.get("ask1_vol", 0),
            open=data.get("open", 0.0),
            high=data.get("high", 0.0),
            low=data.get("low", 0.0),
            pre_close=data.get("pre_close", 0.0),
            bid_prices=[
                data.get("bid1_price", 0.0), data.get("bid2_price", 0.0),
                data.get("bid3_price", 0.0), data.get("bid4_price", 0.0),
                data.get("bid5_price", 0.0)
            ],
            bid_volumes=[
                data.get("bid1_vol", 0), data.get("bid2_vol", 0),
                data.get("bid3_vol", 0), data.get("bid4_vol", 0),
                data.get("bid5_vol", 0)
            ],
            ask_prices=[
                data.get("ask1_price", 0.0), data.get("ask2_price", 0.0),
                data.get("ask3_price", 0.0), data.get("ask4_price", 0.0),
                data.get("ask5_price", 0.0)
            ],
            ask_volumes=[
                data.get("ask1_vol", 0), data.get("ask2_vol", 0),
                data.get("ask3_vol", 0), data.get("ask4_vol", 0),
                data.get("ask5_vol", 0)
            ]
        )
    
    def _flush_buffer(self):
        """刷新缓冲区"""
        if not self.tick_buffer:
            return
        
        # 批量发送
        batch_data = {}
        for stock_code, tick_data in self.tick_buffer.items():
            batch_data[stock_code] = asdict(tick_data)
        
        self.publisher.publish_tick_batch(batch_data)
        
        # 清空缓冲区
        self.tick_buffer.clear()
        self.last_flush_time = time.time()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.publisher.get_stats()
        stats.update({
            "buffer_size": len(self.tick_buffer),
            "batch_mode": self.batch_mode,
            "flush_interval": self.flush_interval
        })
        return stats


class StockDataSubscriber:
    """
    股票数据订阅者
    高性能订阅和处理股票tick数据
    """
    
    def __init__(self, server_address: str = "localhost", port: int = 5555,
                 serialization: str = "msgpack"):
        """
        初始化股票数据订阅者
        
        Args:
            server_address: 服务器地址
            port: 服务器端口
            serialization: 序列化方式
        """
        self.subscriber = ZMQSubscriber(server_address, port, serialization, hwm=50000)
        self.is_running = False
        
        # 数据处理
        self.tick_handlers: Dict[str, List[Callable]] = {}
        self.market_handlers: List[Callable] = []
        
        # 数据缓存
        self.latest_ticks: Dict[str, StockTickData] = {}
        self.tick_queue = Queue(maxsize=10000)
        
    def start(self):
        """启动订阅者"""
        self.subscriber.start()
        self.is_running = True
        logger.info("Stock Data Subscriber started")
    
    def stop(self):
        """停止订阅者"""
        self.is_running = False
        self.subscriber.stop()
        logger.info("Stock Data Subscriber stopped")
    
    def subscribe_stock(self, stock_code: str, handler: Callable[[StockTickData], None]):
        """
        订阅特定股票
        
        Args:
            stock_code: 股票代码
            handler: 处理函数
        """
        if stock_code not in self.tick_handlers:
            self.tick_handlers[stock_code] = []
        self.tick_handlers[stock_code].append(handler)
        
        # 订阅ZMQ主题
        def zmq_handler(topic: str, data: Dict[str, Any]):
            tick_data = StockTickData(**data)
            self.latest_ticks[stock_code] = tick_data
            self.tick_queue.put(tick_data)
            
            # 调用用户处理函数
            for h in self.tick_handlers[stock_code]:
                try:
                    h(tick_data)
                except Exception as e:
                    logger.error(f"Handler error for {stock_code}: {e}")
        
        self.subscriber.subscribe_stock_tick(stock_code, zmq_handler)
        logger.info(f"Subscribed to stock: {stock_code}")
    
    def subscribe_stocks(self, stock_codes: List[str],
                        handler: Callable[[StockTickData], None]):
        """批量订阅股票 - 单独处理每个股票"""
        for stock_code in stock_codes:
            self.subscribe_stock(stock_code, handler)

    def subscribe_stock_list(self, stock_codes: List[str],
                           handler: Callable[[Dict[str, StockTickData]], None]):
        """
        订阅股票列表，返回批量数据字典

        Args:
            stock_codes: 股票代码列表
            handler: 处理函数，接收 {stock_code: StockTickData} 格式的数据
        """
        def zmq_batch_handler(batch_data: Dict[str, Any]):
            # 转换为StockTickData对象
            tick_dict = {}
            for stock_code, data in batch_data.items():
                if stock_code in stock_codes:
                    tick_data = StockTickData(**data)
                    tick_dict[stock_code] = tick_data
                    # 更新缓存
                    self.latest_ticks[stock_code] = tick_data
                    self.tick_queue.put(tick_data)

            if tick_dict:
                try:
                    handler(tick_dict)
                except Exception as e:
                    logger.error(f"Handler error for stock list: {e}")

        # 使用ZMQ订阅者的股票列表订阅功能
        self.subscriber.subscribe_stock_list(stock_codes, zmq_batch_handler)
        logger.info(f"Subscribed to stock list: {stock_codes}")

    def subscribe_stock_list_individual(self, stock_codes: List[str],
                                      handler: Callable[[Dict[str, StockTickData]], None]):
        """
        订阅股票列表，通过单个股票主题聚合

        Args:
            stock_codes: 股票代码列表
            handler: 处理函数，接收 {stock_code: StockTickData} 格式的数据
        """
        def zmq_individual_handler(batch_data: Dict[str, Any]):
            # 转换为StockTickData对象
            tick_dict = {}
            for stock_code, data in batch_data.items():
                tick_data = StockTickData(**data)
                tick_dict[stock_code] = tick_data
                # 更新缓存
                self.latest_ticks[stock_code] = tick_data
                self.tick_queue.put(tick_data)

            try:
                handler(tick_dict)
            except Exception as e:
                logger.error(f"Handler error for individual stock list: {e}")

        # 使用ZMQ订阅者的单个股票聚合功能
        self.subscriber.subscribe_stock_list_individual(stock_codes, zmq_individual_handler)
        logger.info(f"Subscribed to individual stock list: {stock_codes}")
    
    def subscribe_all_stocks(self, handler: Callable[[StockTickData], None]):
        """
        订阅所有股票
        
        Args:
            handler: 处理函数
        """
        def zmq_handler(topic: str, data: Dict[str, Any]):
            tick_data = StockTickData(**data)
            stock_code = tick_data.stock_code
            self.latest_ticks[stock_code] = tick_data
            self.tick_queue.put(tick_data)
            
            try:
                handler(tick_data)
            except Exception as e:
                logger.error(f"Handler error for all stocks: {e}")
        
        self.subscriber.subscribe_all_ticks(zmq_handler)
        logger.info("Subscribed to all stocks")
    
    def subscribe_market_snapshot(self, handler: Callable[[Dict[str, Any]], None]):
        """
        订阅市场快照
        
        Args:
            handler: 处理函数
        """
        self.market_handlers.append(handler)
        
        def zmq_handler(topic: str, data: Dict[str, Any]):
            try:
                handler(data)
            except Exception as e:
                logger.error(f"Market snapshot handler error: {e}")
        
        self.subscriber.subscribe("market.snapshot", zmq_handler)
        logger.info("Subscribed to market snapshot")
    
    def get_latest_tick(self, stock_code: str) -> Optional[StockTickData]:
        """获取最新tick数据"""
        return self.latest_ticks.get(stock_code)
    
    def get_latest_ticks(self, stock_codes: List[str] = None) -> Dict[str, StockTickData]:
        """获取多个股票的最新tick数据"""
        if stock_codes is None:
            return self.latest_ticks.copy()
        
        result = {}
        for stock_code in stock_codes:
            if stock_code in self.latest_ticks:
                result[stock_code] = self.latest_ticks[stock_code]
        return result
    
    def get_tick_queue(self) -> Queue:
        """获取tick数据队列"""
        return self.tick_queue
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.subscriber.get_stats()
        stats.update({
            "subscribed_stocks": len(self.tick_handlers),
            "latest_ticks_count": len(self.latest_ticks),
            "queue_size": self.tick_queue.qsize()
        })
        return stats
