"""
ZMQ股票数据传输示例代码
展示如何使用发布订阅和RPC功能
"""

import time
import threading
import random
from typing import Dict, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

from . import (
    StockDataPublisher, 
    StockDataSubscriber,
    StockDataRPCService,
    StockDataRPCClient,
    StockTickData
)


def example_publisher():
    """发布者示例"""
    print("=== 股票数据发布者示例 ===")
    
    # 创建发布者
    publisher = StockDataPublisher(port=5555)
    publisher.start()
    
    # 模拟股票代码
    stock_codes = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH", "000858.SZ"]
    
    try:
        print("开始发布股票tick数据...")
        for i in range(100):
            # 生成模拟tick数据
            for stock_code in stock_codes:
                tick_data = {
                    "stock_code": stock_code,
                    "timestamp": int(time.time() * 1000),
                    "last_price": round(10 + random.uniform(-2, 2), 2),
                    "volume": random.randint(100, 10000),
                    "amount": random.randint(1000, 100000),
                    "bid1_price": round(10 + random.uniform(-2, 2), 2),
                    "bid1_vol": random.randint(100, 1000),
                    "ask1_price": round(10 + random.uniform(-2, 2), 2),
                    "ask1_vol": random.randint(100, 1000),
                    "open": round(10 + random.uniform(-1, 1), 2),
                    "high": round(10 + random.uniform(0, 3), 2),
                    "low": round(10 + random.uniform(-3, 0), 2),
                    "pre_close": 10.0
                }
                
                # 发布数据
                publisher.publish_tick(tick_data)
            
            print(f"已发布第 {i+1} 批数据")
            time.sleep(0.1)  # 100ms间隔
        
        # 显示统计信息
        stats = publisher.get_stats()
        print(f"发布统计: {stats}")
        
    except KeyboardInterrupt:
        print("停止发布...")
    finally:
        publisher.stop()


def example_subscriber():
    """订阅者示例"""
    print("=== 股票数据订阅者示例 ===")
    
    # 创建订阅者
    subscriber = StockDataSubscriber(server_address="localhost", port=5555)
    subscriber.start()
    
    # 数据统计
    received_count = 0
    
    def on_tick_data(tick_data: StockTickData):
        nonlocal received_count
        received_count += 1
        print(f"收到tick [{received_count}]: {tick_data.stock_code} - "
              f"价格: {tick_data.last_price}, 成交量: {tick_data.volume}")
    
    def on_specific_stock(tick_data: StockTickData):
        print(f"[特定订阅] {tick_data.stock_code}: {tick_data.last_price}")
    
    try:
        # 订阅所有股票
        subscriber.subscribe_all_stocks(on_tick_data)
        
        # 订阅特定股票
        subscriber.subscribe_stock("000001.SZ", on_specific_stock)
        
        print("开始接收数据，按Ctrl+C停止...")
        
        # 运行30秒
        time.sleep(30)
        
        # 显示统计信息
        stats = subscriber.get_stats()
        print(f"订阅统计: {stats}")
        
    except KeyboardInterrupt:
        print("停止订阅...")
    finally:
        subscriber.stop()


def example_rpc_service():
    """RPC服务示例"""
    print("=== 股票数据RPC服务示例 ===")
    
    # 模拟数据管理器
    class MockDataManager:
        def __init__(self):
            self.full_ticks = {}
            self.code_list = ["000001.SZ", "000002.SZ", "600000.SH"]
            
            # 生成模拟数据
            for code in self.code_list:
                self.full_ticks[code] = {
                    "stock_code": code,
                    "timestamp": int(time.time() * 1000),
                    "last_price": round(10 + random.uniform(-2, 2), 2),
                    "volume": random.randint(1000, 100000),
                    "amount": random.randint(10000, 1000000),
                    "pre_close": 10.0
                }
    
    # 创建RPC服务
    data_manager = MockDataManager()
    service = StockDataRPCService(port=5556, data_manager=data_manager)
    service.start()
    
    try:
        print("RPC服务已启动，等待客户端连接...")
        print("按Ctrl+C停止服务")
        
        # 定期更新模拟数据
        def update_data():
            while True:
                for code in data_manager.code_list:
                    data_manager.full_ticks[code]["last_price"] = round(
                        10 + random.uniform(-2, 2), 2)
                    data_manager.full_ticks[code]["timestamp"] = int(time.time() * 1000)
                time.sleep(1)
        
        update_thread = threading.Thread(target=update_data, daemon=True)
        update_thread.start()
        
        # 保持服务运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("停止RPC服务...")
    finally:
        service.stop()


def example_rpc_client():
    """RPC客户端示例"""
    print("=== 股票数据RPC客户端示例 ===")
    
    # 创建RPC客户端
    client = StockDataRPCClient(server_address="localhost", port=5556)
    
    try:
        # 连接到服务器
        client.connect()
        print("已连接到RPC服务器")
        
        # 测试心跳
        result = client.ping()
        print(f"心跳测试: {result}")
        
        # 获取最新tick数据
        result = client.get_latest_tick("000001.SZ")
        print(f"最新tick数据: {result}")
        
        # 获取所有tick数据
        result = client.get_latest_ticks()
        print(f"所有tick数据: {result}")
        
        # 获取市场快照
        result = client.get_market_snapshot()
        print(f"市场快照: {result}")
        
        # 获取历史数据
        result = client.get_historical_data("000001.SZ", count=10)
        print(f"历史数据: {result}")
        
    except Exception as e:
        print(f"RPC客户端错误: {e}")
    finally:
        client.disconnect()


def example_integrated_system():
    """完整系统示例"""
    print("=== 完整股票数据系统示例 ===")
    
    # 模拟数据管理器
    class IntegratedDataManager:
        def __init__(self):
            self.full_ticks = {}
            self.code_list = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"]
            
        def update_tick(self, tick_data):
            self.full_ticks[tick_data["stock_code"]] = tick_data
    
    data_manager = IntegratedDataManager()
    
    # 启动发布者
    publisher = StockDataPublisher(port=5555)
    publisher.start()
    
    # 启动RPC服务
    rpc_service = StockDataRPCService(port=5556, data_manager=data_manager)
    rpc_service.start()
    
    # 启动订阅者
    subscriber = StockDataSubscriber(server_address="localhost", port=5555)
    subscriber.start()
    
    # 订阅数据并更新到数据管理器
    def on_tick_update(tick_data: StockTickData):
        tick_dict = {
            "stock_code": tick_data.stock_code,
            "timestamp": tick_data.timestamp,
            "last_price": tick_data.last_price,
            "volume": tick_data.volume,
            "amount": tick_data.amount,
            "pre_close": tick_data.pre_close
        }
        data_manager.update_tick(tick_dict)
        print(f"更新数据: {tick_data.stock_code} - {tick_data.last_price}")
    
    subscriber.subscribe_all_stocks(on_tick_update)
    
    try:
        print("完整系统已启动...")
        
        # 模拟数据生成
        def generate_data():
            for i in range(50):
                for stock_code in data_manager.code_list:
                    tick_data = {
                        "stock_code": stock_code,
                        "timestamp": int(time.time() * 1000),
                        "last_price": round(10 + random.uniform(-2, 2), 2),
                        "volume": random.randint(100, 10000),
                        "amount": random.randint(1000, 100000),
                        "pre_close": 10.0
                    }
                    publisher.publish_tick(tick_data)
                
                time.sleep(0.5)
        
        # 启动数据生成线程
        data_thread = threading.Thread(target=generate_data, daemon=True)
        data_thread.start()
        
        # 测试RPC客户端
        time.sleep(2)  # 等待一些数据
        
        rpc_client = StockDataRPCClient(server_address="localhost", port=5556)
        rpc_client.connect()
        
        for i in range(10):
            result = rpc_client.get_latest_ticks()
            if result.get("success"):
                print(f"RPC查询 [{i+1}]: 获取到 {result['data']['count']} 只股票数据")
            time.sleep(2)
        
        rpc_client.disconnect()
        
    except KeyboardInterrupt:
        print("停止系统...")
    finally:
        subscriber.stop()
        publisher.stop()
        rpc_service.stop()


def run_performance_test():
    """性能测试"""
    print("=== 性能测试 ===")
    
    # 创建发布者
    publisher = StockDataPublisher(port=5555)
    publisher.start()
    
    # 创建订阅者
    subscriber = StockDataSubscriber(server_address="localhost", port=5555)
    subscriber.start()
    
    # 性能统计
    received_count = 0
    start_time = time.time()
    
    def on_performance_tick(tick_data: StockTickData):
        nonlocal received_count
        received_count += 1
    
    subscriber.subscribe_all_stocks(on_performance_tick)
    
    try:
        print("开始性能测试...")
        
        # 高频发送数据
        stock_codes = [f"{i:06d}.SZ" for i in range(1, 101)]  # 100只股票
        
        test_start = time.time()
        sent_count = 0
        
        for round_num in range(100):  # 100轮
            for stock_code in stock_codes:
                tick_data = {
                    "stock_code": stock_code,
                    "timestamp": int(time.time() * 1000),
                    "last_price": round(10 + random.uniform(-2, 2), 2),
                    "volume": random.randint(100, 1000),
                    "amount": random.randint(1000, 10000)
                }
                publisher.publish_tick(tick_data)
                sent_count += 1
            
            if round_num % 10 == 0:
                elapsed = time.time() - test_start
                print(f"已发送 {sent_count} 条消息，用时 {elapsed:.2f}s，"
                      f"速度: {sent_count/elapsed:.0f} msg/s")
        
        # 等待接收完成
        time.sleep(2)
        
        # 计算性能
        total_time = time.time() - test_start
        print(f"\n性能测试结果:")
        print(f"发送消息数: {sent_count}")
        print(f"接收消息数: {received_count}")
        print(f"总用时: {total_time:.2f}s")
        print(f"发送速度: {sent_count/total_time:.0f} msg/s")
        print(f"接收速度: {received_count/total_time:.0f} msg/s")
        print(f"消息丢失率: {(sent_count-received_count)/sent_count*100:.2f}%")
        
        # 获取详细统计
        pub_stats = publisher.get_stats()
        sub_stats = subscriber.get_stats()
        print(f"\n发布者统计: {pub_stats}")
        print(f"订阅者统计: {sub_stats}")
        
    except KeyboardInterrupt:
        print("性能测试中断")
    finally:
        subscriber.stop()
        publisher.stop()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("python examples.py publisher    # 运行发布者示例")
        print("python examples.py subscriber   # 运行订阅者示例") 
        print("python examples.py rpc_service  # 运行RPC服务示例")
        print("python examples.py rpc_client   # 运行RPC客户端示例")
        print("python examples.py integrated   # 运行完整系统示例")
        print("python examples.py performance  # 运行性能测试")
        sys.exit(1)
    
    mode = sys.argv[1]
    
    if mode == "publisher":
        example_publisher()
    elif mode == "subscriber":
        example_subscriber()
    elif mode == "rpc_service":
        example_rpc_service()
    elif mode == "rpc_client":
        example_rpc_client()
    elif mode == "integrated":
        example_integrated_system()
    elif mode == "performance":
        run_performance_test()
    else:
        print(f"未知模式: {mode}")
        sys.exit(1)
