"""
高性能ZMQ发布订阅模块
专为股票tick数据传输优化，支持高频数据发布和订阅
"""

import zmq
import json
import pickle
import threading
import time
import logging
from typing import Dict, List, Callable, Optional, Any, Union
from dataclasses import asdict
import msgpack
from queue import Queue, Empty

logger = logging.getLogger(__name__)


class ZMQPublisher:
    """
    高性能ZMQ发布者
    支持多种序列化方式，针对股票tick数据优化
    """
    
    def __init__(self, port: int = 5555, bind_address: str = "*", 
                 serialization: str = "msgpack", hwm: int = 10000):
        """
        初始化发布者
        
        Args:
            port: 绑定端口
            bind_address: 绑定地址
            serialization: 序列化方式 ("json", "pickle", "msgpack")
            hwm: 高水位标记，控制内存使用
        """
        self.port = port
        self.bind_address = bind_address
        self.serialization = serialization
        self.hwm = hwm
        
        # ZMQ上下文和socket
        self.context = zmq.Context()
        self.socket = None
        self.is_running = False
        
        # 性能统计
        self.sent_count = 0
        self.last_stats_time = time.time()
        
    def start(self):
        """启动发布者"""
        if self.is_running:
            logger.warning("Publisher already running")
            return
            
        try:
            self.socket = self.context.socket(zmq.PUB)
            # 设置高水位标记
            self.socket.set_hwm(self.hwm)
            # 设置无延迟模式
            self.socket.setsockopt(zmq.TCP_KEEPALIVE, 1)
            self.socket.setsockopt(zmq.TCP_KEEPALIVE_IDLE, 600)
            self.socket.setsockopt(zmq.TCP_KEEPALIVE_INTVL, 60)
            
            bind_addr = f"tcp://{self.bind_address}:{self.port}"
            self.socket.bind(bind_addr)
            self.is_running = True
            logger.info(f"ZMQ Publisher started on {bind_addr}")
            
        except Exception as e:
            logger.error(f"Failed to start publisher: {e}")
            raise
    
    def stop(self):
        """停止发布者"""
        if not self.is_running:
            return
            
        self.is_running = False
        if self.socket:
            self.socket.close()
        self.context.term()
        logger.info("ZMQ Publisher stopped")
    
    def _serialize(self, data: Any) -> bytes:
        """序列化数据"""
        try:
            if self.serialization == "json":
                return json.dumps(data, ensure_ascii=False).encode('utf-8')
            elif self.serialization == "pickle":
                return pickle.dumps(data, protocol=pickle.HIGHEST_PROTOCOL)
            elif self.serialization == "msgpack":
                return msgpack.packb(data, use_bin_type=True)
            else:
                raise ValueError(f"Unsupported serialization: {self.serialization}")
        except Exception as e:
            logger.error(f"Serialization error: {e}")
            raise
    
    def publish(self, topic: str, data: Any, non_blocking: bool = True):
        """
        发布数据
        
        Args:
            topic: 主题
            data: 数据
            non_blocking: 非阻塞模式
        """
        if not self.is_running:
            logger.warning("Publisher not running")
            return False
            
        try:
            # 如果是dataclass对象，转换为字典
            if hasattr(data, '__dataclass_fields__'):
                data = asdict(data)
            
            # 序列化数据
            serialized_data = self._serialize(data)
            
            # 发送消息 [topic][data]
            if non_blocking:
                self.socket.send_multipart([topic.encode('utf-8'), serialized_data], zmq.NOBLOCK)
            else:
                self.socket.send_multipart([topic.encode('utf-8'), serialized_data])
            
            self.sent_count += 1
            return True
            
        except zmq.Again:
            logger.warning("Send buffer full, message dropped")
            return False
        except Exception as e:
            logger.error(f"Publish error: {e}")
            return False
    
    def publish_tick_batch(self, tick_data: Dict[str, Any]):
        """
        批量发布tick数据
        
        Args:
            tick_data: {stock_code: tick_dict} 格式的数据
        """
        if not self.is_running:
            return False
            
        success_count = 0
        for stock_code, tick in tick_data.items():
            if self.publish(f"tick.{stock_code}", tick):
                success_count += 1
        
        return success_count == len(tick_data)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        current_time = time.time()
        elapsed = current_time - self.last_stats_time
        
        stats = {
            "sent_count": self.sent_count,
            "messages_per_second": self.sent_count / elapsed if elapsed > 0 else 0,
            "is_running": self.is_running,
            "serialization": self.serialization
        }
        
        # 重置统计
        self.sent_count = 0
        self.last_stats_time = current_time
        
        return stats


class ZMQSubscriber:
    """
    高性能ZMQ订阅者
    支持多主题订阅和异步处理
    """
    
    def __init__(self, server_address: str = "localhost", port: int = 5555,
                 serialization: str = "msgpack", hwm: int = 10000):
        """
        初始化订阅者
        
        Args:
            server_address: 服务器地址
            port: 服务器端口
            serialization: 序列化方式
            hwm: 高水位标记
        """
        self.server_address = server_address
        self.port = port
        self.serialization = serialization
        self.hwm = hwm
        
        # ZMQ上下文和socket
        self.context = zmq.Context()
        self.socket = None
        self.is_running = False
        
        # 订阅管理
        self.subscriptions = set()
        self.callbacks: Dict[str, List[Callable]] = {}
        
        # 接收线程
        self.receive_thread = None
        
        # 性能统计
        self.received_count = 0
        self.last_stats_time = time.time()
        
    def start(self):
        """启动订阅者"""
        if self.is_running:
            logger.warning("Subscriber already running")
            return
            
        try:
            self.socket = self.context.socket(zmq.SUB)
            # 设置高水位标记
            self.socket.set_hwm(self.hwm)
            # 设置接收超时
            self.socket.setsockopt(zmq.RCVTIMEO, 1000)
            
            connect_addr = f"tcp://{self.server_address}:{self.port}"
            self.socket.connect(connect_addr)
            
            self.is_running = True
            
            # 启动接收线程
            self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
            self.receive_thread.start()
            
            logger.info(f"ZMQ Subscriber connected to {connect_addr}")
            
        except Exception as e:
            logger.error(f"Failed to start subscriber: {e}")
            raise
    
    def stop(self):
        """停止订阅者"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        if self.receive_thread:
            self.receive_thread.join(timeout=2.0)
            
        if self.socket:
            self.socket.close()
        self.context.term()
        logger.info("ZMQ Subscriber stopped")
    
    def subscribe(self, topic: str, callback: Callable[[str, Any], None]):
        """
        订阅主题
        
        Args:
            topic: 主题名称
            callback: 回调函数，接收(topic, data)参数
        """
        if topic not in self.subscriptions:
            if self.socket:
                self.socket.setsockopt_string(zmq.SUBSCRIBE, topic)
            self.subscriptions.add(topic)
            
        if topic not in self.callbacks:
            self.callbacks[topic] = []
        self.callbacks[topic].append(callback)
        
        logger.info(f"Subscribed to topic: {topic}")
    
    def unsubscribe(self, topic: str, callback: Optional[Callable] = None):
        """取消订阅"""
        if topic in self.subscriptions:
            if callback and topic in self.callbacks:
                if callback in self.callbacks[topic]:
                    self.callbacks[topic].remove(callback)
                if not self.callbacks[topic]:
                    del self.callbacks[topic]
                    if self.socket:
                        self.socket.setsockopt_string(zmq.UNSUBSCRIBE, topic)
                    self.subscriptions.remove(topic)
            else:
                # 取消所有回调
                if topic in self.callbacks:
                    del self.callbacks[topic]
                if self.socket:
                    self.socket.setsockopt_string(zmq.UNSUBSCRIBE, topic)
                self.subscriptions.remove(topic)
                
        logger.info(f"Unsubscribed from topic: {topic}")
    
    def subscribe_all_ticks(self, callback: Callable[[str, Any], None]):
        """订阅所有tick数据"""
        self.subscribe("tick.", callback)
    
    def subscribe_stock_tick(self, stock_code: str, callback: Callable[[str, Any], None]):
        """订阅特定股票的tick数据"""
        self.subscribe(f"tick.{stock_code}", callback)
    
    def _deserialize(self, data: bytes) -> Any:
        """反序列化数据"""
        try:
            if self.serialization == "json":
                return json.loads(data.decode('utf-8'))
            elif self.serialization == "pickle":
                return pickle.loads(data)
            elif self.serialization == "msgpack":
                return msgpack.unpackb(data, raw=False)
            else:
                raise ValueError(f"Unsupported serialization: {self.serialization}")
        except Exception as e:
            logger.error(f"Deserialization error: {e}")
            raise
    
    def _receive_loop(self):
        """接收循环"""
        while self.is_running:
            try:
                # 接收消息
                message = self.socket.recv_multipart(zmq.NOBLOCK)
                if len(message) >= 2:
                    topic = message[0].decode('utf-8')
                    data = self._deserialize(message[1])
                    
                    # 调用回调函数
                    self._handle_message(topic, data)
                    self.received_count += 1
                    
            except zmq.Again:
                # 超时，继续循环
                continue
            except Exception as e:
                if self.is_running:
                    logger.error(f"Receive error: {e}")
                break
    
    def _handle_message(self, topic: str, data: Any):
        """处理接收到的消息"""
        # 查找匹配的回调
        for sub_topic, callbacks in self.callbacks.items():
            if topic.startswith(sub_topic):
                for callback in callbacks:
                    try:
                        callback(topic, data)
                    except Exception as e:
                        logger.error(f"Callback error for topic {topic}: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        current_time = time.time()
        elapsed = current_time - self.last_stats_time
        
        stats = {
            "received_count": self.received_count,
            "messages_per_second": self.received_count / elapsed if elapsed > 0 else 0,
            "is_running": self.is_running,
            "subscriptions": list(self.subscriptions),
            "serialization": self.serialization
        }
        
        # 重置统计
        self.received_count = 0
        self.last_stats_time = current_time
        
        return stats
