"""
FastAPI客户端测试脚本
用于测试FastAPI服务器的各个接口
"""

import requests
import json
import time
from typing import Dict, Any, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class FastAPIClient:
    """FastAPI客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化客户端
        
        Args:
            base_url: API服务器基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """GET请求"""
        return self._make_request("GET", endpoint, params=params)
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return self.get("/health")
    
    def get_api_info(self) -> Dict[str, Any]:
        """获取API信息"""
        return self.get("/")
    
    def get_latest_ticks(self, stock_codes: Optional[str] = None) -> Dict[str, Any]:
        """获取最新tick数据"""
        params = {"stock_codes": stock_codes} if stock_codes else None
        return self.get("/api/v1/tick/latest", params=params)
    
    def get_stock_tick(self, stock_code: str) -> Dict[str, Any]:
        """获取指定股票tick数据"""
        return self.get(f"/api/v1/tick/{stock_code}")
    
    def get_minute_data(self, stock_code: str, count: int = 240, 
                       period: str = "1m") -> Dict[str, Any]:
        """获取分钟数据"""
        params = {"count": count, "period": period}
        return self.get(f"/api/v1/minute/{stock_code}", params=params)
    
    def get_daily_data(self, stock_code: str, count: int = 30,
                      start_date: Optional[str] = None,
                      end_date: Optional[str] = None) -> Dict[str, Any]:
        """获取日线数据"""
        params = {"count": count}
        if start_date:
            params["start_date"] = start_date
        if end_date:
            params["end_date"] = end_date
        return self.get(f"/api/v1/daily/{stock_code}", params=params)
    
    def get_market_snapshot(self) -> Dict[str, Any]:
        """获取市场快照"""
        return self.get("/api/v1/market/snapshot")
    
    def get_server_stats(self) -> Dict[str, Any]:
        """获取服务器统计"""
        return self.get("/api/v1/stats")


def test_basic_apis():
    """测试基础API接口"""
    print("=== 测试基础API接口 ===")
    
    client = FastAPIClient("http://localhost:8000")
    
    # 1. 健康检查
    print("\n1. 健康检查")
    result = client.health_check()
    print(f"健康检查: {result.get('success', False)}")
    if result.get('success'):
        print(f"状态: {result['data']['status']}")
    
    # 2. API信息
    print("\n2. API信息")
    result = client.get_api_info()
    print(f"API信息: {result.get('success', False)}")
    if result.get('success'):
        print(f"API名称: {result['data']['name']}")
        print(f"版本: {result['data']['version']}")
    
    # 3. 服务器统计
    print("\n3. 服务器统计")
    result = client.get_server_stats()
    print(f"服务器统计: {result.get('success', False)}")
    if result.get('success'):
        print(f"服务状态: {result['data']['server_status']}")
        print(f"数据管理器可用: {result['data']['data_manager_available']}")


def test_tick_apis():
    """测试tick数据API"""
    print("\n=== 测试Tick数据API ===")
    
    client = FastAPIClient("http://localhost:8000")
    
    # 1. 获取所有最新tick数据
    print("\n1. 获取所有最新tick数据")
    result = client.get_latest_ticks()
    print(f"获取成功: {result.get('success', False)}")
    if result.get('success'):
        print(f"股票数量: {result.get('count', 0)}")
        for stock_code, tick_data in result['data'].items():
            print(f"  {stock_code}: 价格={tick_data['last_price']}, 成交量={tick_data['volume']}")
    
    # 2. 获取指定股票列表的tick数据
    print("\n2. 获取指定股票列表tick数据")
    result = client.get_latest_ticks("000001.SZ,000002.SZ")
    print(f"获取成功: {result.get('success', False)}")
    if result.get('success'):
        print(f"股票数量: {result.get('count', 0)}")
        for stock_code, tick_data in result['data'].items():
            print(f"  {stock_code}: 价格={tick_data['last_price']}")
    
    # 3. 获取单个股票tick数据
    print("\n3. 获取单个股票tick数据")
    result = client.get_stock_tick("000001.SZ")
    print(f"获取成功: {result.get('success', False)}")
    if result.get('success'):
        tick_data = result['data']
        print(f"  股票代码: {tick_data['stock_code']}")
        print(f"  最新价: {tick_data['last_price']}")
        print(f"  成交量: {tick_data['volume']}")
        print(f"  买一价: {tick_data['bid1_price']}")
        print(f"  卖一价: {tick_data['ask1_price']}")


def test_historical_apis():
    """测试历史数据API"""
    print("\n=== 测试历史数据API ===")
    
    client = FastAPIClient("http://localhost:8000")
    
    # 1. 获取分钟数据
    print("\n1. 获取分钟数据")
    result = client.get_minute_data("000001.SZ", count=10)
    print(f"获取成功: {result.get('success', False)}")
    if result.get('success'):
        print(f"数据条数: {result.get('count', 0)}")
        data = result['data']
        if data:
            print("最近几条分钟数据:")
            for i, minute_data in enumerate(data[-3:]):  # 显示最后3条
                timestamp = minute_data['timestamp']
                dt = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp/1000))
                print(f"  {dt}: 开={minute_data['open']}, 高={minute_data['high']}, "
                      f"低={minute_data['low']}, 收={minute_data['close']}")
    
    # 2. 获取日线数据
    print("\n2. 获取日线数据")
    result = client.get_daily_data("000001.SZ", count=5)
    print(f"获取成功: {result.get('success', False)}")
    if result.get('success'):
        print(f"数据条数: {result.get('count', 0)}")
        data = result['data']
        if data:
            print("最近几条日线数据:")
            for daily_data in data[-3:]:  # 显示最后3条
                print(f"  {daily_data['date']}: 开={daily_data['open']}, "
                      f"高={daily_data['high']}, 低={daily_data['low']}, "
                      f"收={daily_data['close']}, 涨跌幅={daily_data['change_pct']}%")


def test_market_apis():
    """测试市场数据API"""
    print("\n=== 测试市场数据API ===")
    
    client = FastAPIClient("http://localhost:8000")
    
    # 获取市场快照
    print("\n获取市场快照")
    result = client.get_market_snapshot()
    print(f"获取成功: {result.get('success', False)}")
    if result.get('success'):
        market_stats = result['data']['market_stats']
        print(f"总股票数: {market_stats['total_stocks']}")
        print(f"上涨股票: {market_stats['rising_count']}")
        print(f"下跌股票: {market_stats['falling_count']}")
        print(f"上涨比例: {market_stats['rising_ratio']}%")
        
        ticks = result['data']['ticks']
        print(f"tick数据数量: {len(ticks)}")


def test_performance():
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    client = FastAPIClient("http://localhost:8000")
    
    # 测试单个请求延迟
    print("\n1. 单个请求延迟测试")
    start_time = time.time()
    result = client.get_stock_tick("000001.SZ")
    elapsed = time.time() - start_time
    print(f"单个tick请求延迟: {elapsed*1000:.1f}ms")
    print(f"请求成功: {result.get('success', False)}")
    
    # 测试批量请求性能
    print("\n2. 批量请求性能测试")
    stock_codes = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"]
    
    start_time = time.time()
    for _ in range(10):
        for stock_code in stock_codes:
            client.get_stock_tick(stock_code)
    elapsed = time.time() - start_time
    total_requests = 10 * len(stock_codes)
    
    print(f"总请求数: {total_requests}")
    print(f"总用时: {elapsed:.2f}s")
    print(f"平均延迟: {elapsed/total_requests*1000:.1f}ms")
    print(f"QPS: {total_requests/elapsed:.1f}")
    
    # 测试批量获取API
    print("\n3. 批量获取API性能测试")
    stock_codes_str = ",".join(stock_codes)
    
    start_time = time.time()
    for _ in range(10):
        client.get_latest_ticks(stock_codes_str)
    elapsed = time.time() - start_time
    
    print(f"批量请求10次用时: {elapsed:.2f}s")
    print(f"平均延迟: {elapsed/10*1000:.1f}ms")


def run_comprehensive_test():
    """运行综合测试"""
    print("开始FastAPI服务器综合测试...")
    print("请确保FastAPI服务器已启动在 http://localhost:8000")
    
    try:
        # 基础API测试
        test_basic_apis()
        
        time.sleep(1)
        
        # Tick数据API测试
        test_tick_apis()
        
        time.sleep(1)
        
        # 历史数据API测试
        test_historical_apis()
        
        time.sleep(1)
        
        # 市场数据API测试
        test_market_apis()
        
        time.sleep(1)
        
        # 性能测试
        test_performance()
        
        print("\n=== 测试完成 ===")
        print("所有API接口测试完成!")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def test_specific_api(api_name: str):
    """测试特定API"""
    client = FastAPIClient("http://localhost:8000")
    
    if api_name == "health":
        result = client.health_check()
        print(json.dumps(result, indent=2, ensure_ascii=False))
    elif api_name == "tick":
        result = client.get_stock_tick("000001.SZ")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    elif api_name == "ticks":
        result = client.get_latest_ticks("000001.SZ,000002.SZ")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    elif api_name == "minute":
        result = client.get_minute_data("000001.SZ", count=5)
        print(json.dumps(result, indent=2, ensure_ascii=False))
    elif api_name == "daily":
        result = client.get_daily_data("000001.SZ", count=5)
        print(json.dumps(result, indent=2, ensure_ascii=False))
    elif api_name == "market":
        result = client.get_market_snapshot()
        print(json.dumps(result, indent=2, ensure_ascii=False))
    elif api_name == "stats":
        result = client.get_server_stats()
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print(f"未知API: {api_name}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("python test_fastapi_client.py all        # 运行所有测试")
        print("python test_fastapi_client.py basic      # 基础API测试")
        print("python test_fastapi_client.py tick       # Tick数据测试")
        print("python test_fastapi_client.py historical # 历史数据测试")
        print("python test_fastapi_client.py market     # 市场数据测试")
        print("python test_fastapi_client.py performance # 性能测试")
        print("python test_fastapi_client.py health     # 测试健康检查API")
        print("python test_fastapi_client.py stats      # 测试统计API")
        sys.exit(1)
    
    mode = sys.argv[1]
    
    if mode == "all":
        run_comprehensive_test()
    elif mode == "basic":
        test_basic_apis()
    elif mode == "tick":
        test_tick_apis()
    elif mode == "historical":
        test_historical_apis()
    elif mode == "market":
        test_market_apis()
    elif mode == "performance":
        test_performance()
    elif mode in ["health", "tick", "ticks", "minute", "daily", "market", "stats"]:
        test_specific_api(mode)
    else:
        print(f"未知测试模式: {mode}")
        sys.exit(1)
