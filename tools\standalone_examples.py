"""
独立使用ZMQ发布订阅和RPC的示例
展示如何单独使用每个模块
"""

import time
import threading
import random
import json
from typing import Dict, List, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_standalone_pubsub():
    """独立使用ZMQ发布订阅示例"""
    print("=== 独立ZMQ发布订阅示例 ===")
    
    from .zmq_pubsub import ZMQPublisher, ZMQSubscriber
    
    # 创建发布者
    publisher = ZMQPublisher(port=5555, serialization="msgpack")
    publisher.start()
    
    # 创建订阅者
    subscriber = ZMQSubscriber(server_address="localhost", port=5555, serialization="msgpack")
    subscriber.start()
    
    # 股票列表
    stock_codes = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"]
    
    # 订阅股票列表 - 批量数据回调
    def on_stock_list_data(batch_data: Dict[str, Any]):
        print(f"收到批量数据: {list(batch_data.keys())}")
        for stock_code, data in batch_data.items():
            print(f"  {stock_code}: 价格={data.get('last_price', 0)}, 成交量={data.get('volume', 0)}")
    
    # 订阅股票列表
    subscriber.subscribe_stock_list(stock_codes, on_stock_list_data)
    
    # 订阅单个股票
    def on_single_stock(topic: str, data: Any):
        stock_code = topic.replace("tick.", "")
        print(f"单个股票数据: {stock_code} - {data.get('last_price', 0)}")
    
    subscriber.subscribe("tick.000001.SZ", on_single_stock)
    
    try:
        print("开始发布数据...")
        
        for i in range(20):
            # 生成批量数据
            batch_data = {}
            for stock_code in stock_codes:
                tick_data = {
                    "stock_code": stock_code,
                    "timestamp": int(time.time() * 1000),
                    "last_price": round(10 + random.uniform(-2, 2), 2),
                    "volume": random.randint(100, 10000),
                    "amount": random.randint(1000, 100000),
                    "bid1_price": round(10 + random.uniform(-2, 2), 2),
                    "ask1_price": round(10 + random.uniform(-2, 2), 2)
                }
                batch_data[stock_code] = tick_data
            
            # 发布批量数据
            publisher.publish_tick_batch(batch_data)
            print(f"已发布第 {i+1} 批数据")
            
            time.sleep(0.5)
        
        # 显示统计
        pub_stats = publisher.get_stats()
        sub_stats = subscriber.get_stats()
        print(f"发布者统计: {pub_stats}")
        print(f"订阅者统计: {sub_stats}")
        
    except KeyboardInterrupt:
        print("停止...")
    finally:
        subscriber.stop()
        publisher.stop()


def example_standalone_rpc():
    """独立使用ZMQ RPC示例"""
    print("=== 独立ZMQ RPC示例 ===")
    
    from .zmq_rpc import ZMQRPCServer, ZMQRPCClient
    
    # 模拟数据存储
    stock_data = {
        "000001.SZ": {"last_price": 15.68, "volume": 1000, "timestamp": int(time.time() * 1000)},
        "000002.SZ": {"last_price": 25.34, "volume": 2000, "timestamp": int(time.time() * 1000)},
        "600000.SH": {"last_price": 8.92, "volume": 3000, "timestamp": int(time.time() * 1000)}
    }
    
    # 定义RPC方法
    def get_stock_price(stock_code: str) -> Dict[str, Any]:
        """获取股票价格"""
        if stock_code in stock_data:
            return {
                "success": True,
                "data": stock_data[stock_code],
                "timestamp": time.time()
            }
        return {
            "success": False,
            "error": f"Stock {stock_code} not found",
            "timestamp": time.time()
        }
    
    def get_stock_list(stock_codes: List[str]) -> Dict[str, Any]:
        """获取股票列表数据"""
        result = {}
        for code in stock_codes:
            if code in stock_data:
                result[code] = stock_data[code]
        
        return {
            "success": True,
            "data": result,
            "count": len(result),
            "timestamp": time.time()
        }
    
    def update_stock_price(stock_code: str, price: float) -> Dict[str, Any]:
        """更新股票价格"""
        if stock_code in stock_data:
            stock_data[stock_code]["last_price"] = price
            stock_data[stock_code]["timestamp"] = int(time.time() * 1000)
            return {
                "success": True,
                "message": f"Updated {stock_code} price to {price}",
                "timestamp": time.time()
            }
        return {
            "success": False,
            "error": f"Stock {stock_code} not found",
            "timestamp": time.time()
        }
    
    def get_all_stocks() -> Dict[str, Any]:
        """获取所有股票数据"""
        return {
            "success": True,
            "data": stock_data,
            "count": len(stock_data),
            "timestamp": time.time()
        }
    
    # 创建RPC服务器
    server = ZMQRPCServer(port=5556, serialization="msgpack")
    
    # 注册方法
    server.register_methods({
        "get_stock_price": get_stock_price,
        "get_stock_list": get_stock_list,
        "update_stock_price": update_stock_price,
        "get_all_stocks": get_all_stocks,
        "ping": lambda: {"success": True, "message": "pong", "timestamp": time.time()}
    })
    
    # 启动服务器
    server.start()
    
    # 在另一个线程中运行客户端测试
    def test_client():
        time.sleep(1)  # 等待服务器启动
        
        client = ZMQRPCClient(server_address="localhost", port=5556, serialization="msgpack")
        client.connect()
        
        try:
            print("\n--- RPC客户端测试 ---")
            
            # 测试心跳
            result = client.call("ping")
            print(f"心跳测试: {result}")
            
            # 获取单个股票
            result = client.call("get_stock_price", {"stock_code": "000001.SZ"})
            print(f"单个股票: {result}")
            
            # 获取股票列表
            result = client.call("get_stock_list", {"stock_codes": ["000001.SZ", "000002.SZ"]})
            print(f"股票列表: {result}")
            
            # 更新价格
            result = client.call("update_stock_price", {"stock_code": "000001.SZ", "price": 16.88})
            print(f"更新价格: {result}")
            
            # 获取所有股票
            result = client.call("get_all_stocks")
            print(f"所有股票: {result}")
            
            # 性能测试
            print("\n--- 性能测试 ---")
            start_time = time.time()
            for i in range(100):
                client.call("ping")
            elapsed = time.time() - start_time
            print(f"100次ping调用用时: {elapsed:.2f}s, 平均: {elapsed*10:.1f}ms/call")
            
        except Exception as e:
            print(f"客户端错误: {e}")
        finally:
            client.disconnect()
    
    # 启动客户端测试线程
    client_thread = threading.Thread(target=test_client, daemon=True)
    client_thread.start()
    
    try:
        print("RPC服务器已启动，等待客户端连接...")
        
        # 模拟数据更新
        def update_data():
            while True:
                for code in stock_data:
                    # 随机更新价格
                    current_price = stock_data[code]["last_price"]
                    new_price = round(current_price * (1 + random.uniform(-0.02, 0.02)), 2)
                    stock_data[code]["last_price"] = new_price
                    stock_data[code]["timestamp"] = int(time.time() * 1000)
                time.sleep(2)
        
        update_thread = threading.Thread(target=update_data, daemon=True)
        update_thread.start()
        
        # 等待客户端测试完成
        client_thread.join(timeout=30)
        
        # 显示服务器统计
        stats = server.get_stats()
        print(f"\nRPC服务器统计: {stats}")
        
    except KeyboardInterrupt:
        print("停止RPC服务器...")
    finally:
        server.stop()


def example_mixed_usage():
    """混合使用发布订阅和RPC的示例"""
    print("=== 混合使用示例 ===")
    
    from .zmq_pubsub import ZMQPublisher, ZMQSubscriber
    from .zmq_rpc import ZMQRPCServer, ZMQRPCClient
    
    # 共享数据存储
    shared_data = {}
    
    # 1. 启动发布订阅系统
    publisher = ZMQPublisher(port=5555)
    publisher.start()
    
    subscriber = ZMQSubscriber(port=5555)
    subscriber.start()
    
    # 订阅数据并存储到共享存储
    def store_data(topic: str, data: Any):
        if topic.startswith("tick."):
            stock_code = topic.replace("tick.", "")
            shared_data[stock_code] = data
            print(f"存储数据: {stock_code} - {data.get('last_price', 0)}")
    
    subscriber.subscribe("tick.", store_data)
    
    # 2. 启动RPC系统
    def get_stored_data(stock_code: str = None) -> Dict[str, Any]:
        if stock_code:
            return {
                "success": True,
                "data": shared_data.get(stock_code, {}),
                "timestamp": time.time()
            }
        else:
            return {
                "success": True,
                "data": shared_data,
                "count": len(shared_data),
                "timestamp": time.time()
            }
    
    rpc_server = ZMQRPCServer(port=5556)
    rpc_server.register_method("get_data", get_stored_data)
    rpc_server.start()
    
    try:
        print("混合系统已启动...")
        
        # 发布一些数据
        def publish_data():
            stock_codes = ["000001.SZ", "000002.SZ", "600000.SH"]
            for i in range(10):
                for stock_code in stock_codes:
                    data = {
                        "stock_code": stock_code,
                        "last_price": round(10 + random.uniform(-2, 2), 2),
                        "volume": random.randint(100, 1000),
                        "timestamp": int(time.time() * 1000)
                    }
                    publisher.publish(f"tick.{stock_code}", data)
                time.sleep(1)
        
        publish_thread = threading.Thread(target=publish_data, daemon=True)
        publish_thread.start()
        
        # 等待一些数据
        time.sleep(3)
        
        # 通过RPC查询数据
        rpc_client = ZMQRPCClient(port=5556)
        rpc_client.connect()
        
        result = rpc_client.call("get_data")
        print(f"RPC查询所有数据: {result}")
        
        result = rpc_client.call("get_data", {"stock_code": "000001.SZ"})
        print(f"RPC查询单个股票: {result}")
        
        rpc_client.disconnect()
        
        # 等待发布完成
        publish_thread.join(timeout=15)
        
    except KeyboardInterrupt:
        print("停止混合系统...")
    finally:
        subscriber.stop()
        publisher.stop()
        rpc_server.stop()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("python standalone_examples.py pubsub    # 独立发布订阅示例")
        print("python standalone_examples.py rpc       # 独立RPC示例")
        print("python standalone_examples.py mixed     # 混合使用示例")
        sys.exit(1)
    
    mode = sys.argv[1]
    
    if mode == "pubsub":
        example_standalone_pubsub()
    elif mode == "rpc":
        example_standalone_rpc()
    elif mode == "mixed":
        example_mixed_usage()
    else:
        print(f"未知模式: {mode}")
        sys.exit(1)
