"""
高效FastAPI股票数据服务端
提供RESTful API接口用于获取股票数据
"""

from fastapi import FastAPI, HTTPException, Query, Path
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional, Union
import time
import logging
import asyncio
from datetime import datetime, timedelta
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="股票数据API服务",
    description="高性能股票tick数据、分钟数据、日线数据API接口",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型定义
class TickData(BaseModel):
    """Tick数据模型"""
    stock_code: str = Field(..., description="股票代码")
    timestamp: int = Field(..., description="时间戳(毫秒)")
    last_price: float = Field(..., description="最新价")
    volume: int = Field(..., description="成交量")
    amount: float = Field(..., description="成交额")
    open: float = Field(0.0, description="开盘价")
    high: float = Field(0.0, description="最高价")
    low: float = Field(0.0, description="最低价")
    pre_close: float = Field(0.0, description="昨收价")
    bid1_price: float = Field(0.0, description="买一价")
    bid1_vol: int = Field(0, description="买一量")
    ask1_price: float = Field(0.0, description="卖一价")
    ask1_vol: int = Field(0, description="卖一量")

class MinuteData(BaseModel):
    """分钟数据模型"""
    stock_code: str = Field(..., description="股票代码")
    timestamp: int = Field(..., description="时间戳(毫秒)")
    open: float = Field(..., description="开盘价")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    close: float = Field(..., description="收盘价")
    volume: int = Field(..., description="成交量")
    amount: float = Field(..., description="成交额")

class DailyData(BaseModel):
    """日线数据模型"""
    stock_code: str = Field(..., description="股票代码")
    date: str = Field(..., description="日期(YYYY-MM-DD)")
    open: float = Field(..., description="开盘价")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    close: float = Field(..., description="收盘价")
    volume: int = Field(..., description="成交量")
    amount: float = Field(..., description="成交额")
    pre_close: float = Field(..., description="昨收价")
    change: float = Field(..., description="涨跌额")
    change_pct: float = Field(..., description="涨跌幅(%)")

class APIResponse(BaseModel):
    """API响应模型"""
    success: bool = Field(..., description="是否成功")
    data: Any = Field(None, description="数据")
    message: str = Field("", description="消息")
    timestamp: int = Field(..., description="响应时间戳")
    count: Optional[int] = Field(None, description="数据条数")

# 全局数据管理器（后续可以注入实际的数据管理器）
data_manager = None
rpc_client = None

def set_data_manager(manager):
    """设置数据管理器"""
    global data_manager
    data_manager = manager

def set_rpc_client(client):
    """设置RPC客户端"""
    global rpc_client
    rpc_client = client

# 辅助函数
def create_response(success: bool = True, data: Any = None, 
                   message: str = "", count: Optional[int] = None) -> Dict[str, Any]:
    """创建标准响应"""
    response = {
        "success": success,
        "data": data or {},
        "message": message,
        "timestamp": int(time.time() * 1000)
    }
    if count is not None:
        response["count"] = count
    return response

def get_mock_tick_data(stock_code: str) -> Dict[str, Any]:
    """获取模拟tick数据"""
    import random
    return {
        "stock_code": stock_code,
        "timestamp": int(time.time() * 1000),
        "last_price": round(10 + random.uniform(-2, 2), 2),
        "volume": random.randint(1000, 100000),
        "amount": random.randint(10000, 1000000),
        "open": round(10 + random.uniform(-1, 1), 2),
        "high": round(10 + random.uniform(0, 3), 2),
        "low": round(10 + random.uniform(-3, 0), 2),
        "pre_close": 10.0,
        "bid1_price": round(10 + random.uniform(-2, 2), 2),
        "bid1_vol": random.randint(100, 1000),
        "ask1_price": round(10 + random.uniform(-2, 2), 2),
        "ask1_vol": random.randint(100, 1000)
    }

# API路由定义

@app.get("/", response_model=Dict[str, Any])
async def root():
    """根路径，返回API信息"""
    return create_response(
        data={
            "name": "股票数据API服务",
            "version": "1.0.0",
            "description": "提供股票tick数据、分钟数据、日线数据查询接口",
            "endpoints": {
                "tick": "/api/v1/tick",
                "minute": "/api/v1/minute", 
                "daily": "/api/v1/daily",
                "docs": "/docs"
            }
        },
        message="API服务运行正常"
    )

@app.get("/health")
async def health_check():
    """健康检查"""
    return create_response(
        data={"status": "healthy", "uptime": time.time()},
        message="服务健康"
    )

# Tick数据相关接口

@app.get("/api/v1/tick/latest", response_model=Dict[str, Any])
async def get_latest_ticks(
    stock_codes: Optional[str] = Query(None, description="股票代码列表，逗号分隔，如: 000001.SZ,000002.SZ")
):
    """获取最新tick数据"""
    try:
        if data_manager and hasattr(data_manager, 'full_ticks'):
            # 使用实际数据管理器
            if stock_codes:
                codes = [code.strip() for code in stock_codes.split(',')]
                result = {}
                for code in codes:
                    if code in data_manager.full_ticks:
                        result[code] = data_manager.full_ticks[code]
            else:
                result = data_manager.full_ticks
            
            return create_response(
                data=result,
                count=len(result),
                message="获取最新tick数据成功"
            )
        else:
            # 返回模拟数据
            if stock_codes:
                codes = [code.strip() for code in stock_codes.split(',')]
                result = {code: get_mock_tick_data(code) for code in codes}
            else:
                # 默认返回几个常见股票的模拟数据
                default_codes = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"]
                result = {code: get_mock_tick_data(code) for code in default_codes}
            
            return create_response(
                data=result,
                count=len(result),
                message="获取模拟tick数据成功"
            )
            
    except Exception as e:
        logger.error(f"获取最新tick数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

@app.get("/api/v1/tick/{stock_code}", response_model=Dict[str, Any])
async def get_stock_tick(
    stock_code: str = Path(..., description="股票代码，如: 000001.SZ")
):
    """获取指定股票的最新tick数据"""
    try:
        if data_manager and hasattr(data_manager, 'full_ticks'):
            # 使用实际数据管理器
            if stock_code in data_manager.full_ticks:
                result = data_manager.full_ticks[stock_code]
                return create_response(
                    data=result,
                    message=f"获取{stock_code}tick数据成功"
                )
            else:
                return create_response(
                    success=False,
                    message=f"未找到股票{stock_code}的数据"
                )
        else:
            # 返回模拟数据
            result = get_mock_tick_data(stock_code)
            return create_response(
                data=result,
                message=f"获取{stock_code}模拟tick数据成功"
            )
            
    except Exception as e:
        logger.error(f"获取股票{stock_code}tick数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

# 分钟数据接口

@app.get("/api/v1/minute/{stock_code}", response_model=Dict[str, Any])
async def get_minute_data(
    stock_code: str = Path(..., description="股票代码"),
    count: int = Query(240, description="获取数据条数，默认240条(一个交易日)"),
    period: str = Query("1m", description="周期，支持: 1m, 5m, 15m, 30m, 60m")
):
    """获取指定股票的分钟数据"""
    try:
        if data_manager and hasattr(data_manager, 'cash_manger'):
            # 使用实际数据管理器获取历史数据
            ts_manager = data_manager.cash_manger
            if hasattr(ts_manager, 'get_stock_data'):
                df = ts_manager.get_stock_data(stock_code, count)
                if df is not None and not df.empty:
                    result = df.to_dict('records')
                    return create_response(
                        data=result,
                        count=len(result),
                        message=f"获取{stock_code}分钟数据成功"
                    )
        
        # 返回模拟分钟数据
        import random
        result = []
        base_time = int(time.time() * 1000)
        base_price = 10.0
        
        for i in range(count):
            timestamp = base_time - (count - i - 1) * 60 * 1000  # 每分钟
            price_change = random.uniform(-0.1, 0.1)
            base_price += price_change
            
            minute_data = {
                "stock_code": stock_code,
                "timestamp": timestamp,
                "open": round(base_price - random.uniform(0, 0.05), 2),
                "high": round(base_price + random.uniform(0, 0.1), 2),
                "low": round(base_price - random.uniform(0, 0.1), 2),
                "close": round(base_price, 2),
                "volume": random.randint(1000, 10000),
                "amount": random.randint(10000, 100000)
            }
            result.append(minute_data)
        
        return create_response(
            data=result,
            count=len(result),
            message=f"获取{stock_code}模拟分钟数据成功"
        )
        
    except Exception as e:
        logger.error(f"获取{stock_code}分钟数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

# 日线数据接口

@app.get("/api/v1/daily/{stock_code}", response_model=Dict[str, Any])
async def get_daily_data(
    stock_code: str = Path(..., description="股票代码"),
    count: int = Query(30, description="获取数据条数，默认30条"),
    start_date: Optional[str] = Query(None, description="开始日期，格式: YYYY-MM-DD"),
    end_date: Optional[str] = Query(None, description="结束日期，格式: YYYY-MM-DD")
):
    """获取指定股票的日线数据"""
    try:
        # 目前返回模拟日线数据
        import random
        from datetime import datetime, timedelta
        
        result = []
        base_price = 10.0
        
        # 计算日期范围
        if start_date and end_date:
            start = datetime.strptime(start_date, "%Y-%m-%d")
            end = datetime.strptime(end_date, "%Y-%m-%d")
            days = (end - start).days + 1
            count = min(count, days)
        else:
            start = datetime.now() - timedelta(days=count-1)
        
        for i in range(count):
            current_date = start + timedelta(days=i)
            # 跳过周末
            if current_date.weekday() >= 5:
                continue
                
            price_change = random.uniform(-0.5, 0.5)
            base_price += price_change
            
            open_price = round(base_price + random.uniform(-0.2, 0.2), 2)
            close_price = round(base_price, 2)
            high_price = round(max(open_price, close_price) + random.uniform(0, 0.3), 2)
            low_price = round(min(open_price, close_price) - random.uniform(0, 0.3), 2)
            pre_close = round(base_price - price_change, 2)
            
            daily_data = {
                "stock_code": stock_code,
                "date": current_date.strftime("%Y-%m-%d"),
                "open": open_price,
                "high": high_price,
                "low": low_price,
                "close": close_price,
                "volume": random.randint(100000, 1000000),
                "amount": random.randint(1000000, 10000000),
                "pre_close": pre_close,
                "change": round(close_price - pre_close, 2),
                "change_pct": round((close_price - pre_close) / pre_close * 100, 2)
            }
            result.append(daily_data)
        
        return create_response(
            data=result,
            count=len(result),
            message=f"获取{stock_code}模拟日线数据成功"
        )
        
    except Exception as e:
        logger.error(f"获取{stock_code}日线数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

# 市场数据接口

@app.get("/api/v1/market/snapshot", response_model=Dict[str, Any])
async def get_market_snapshot():
    """获取市场快照"""
    try:
        if data_manager and hasattr(data_manager, 'full_ticks'):
            # 使用实际数据
            all_ticks = data_manager.full_ticks
            
            # 计算市场统计
            total_stocks = len(all_ticks)
            rising_count = 0
            falling_count = 0
            unchanged_count = 0
            
            for tick in all_ticks.values():
                last_price = tick.get('last_price', 0)
                pre_close = tick.get('pre_close', 0)
                if last_price > pre_close:
                    rising_count += 1
                elif last_price < pre_close:
                    falling_count += 1
                else:
                    unchanged_count += 1
            
            market_stats = {
                "total_stocks": total_stocks,
                "rising_count": rising_count,
                "falling_count": falling_count,
                "unchanged_count": unchanged_count,
                "rising_ratio": round(rising_count / total_stocks * 100, 2) if total_stocks > 0 else 0,
                "update_time": int(time.time() * 1000)
            }
            
            return create_response(
                data={
                    "market_stats": market_stats,
                    "ticks": all_ticks
                },
                count=total_stocks,
                message="获取市场快照成功"
            )
        else:
            # 返回模拟市场数据
            import random
            stock_codes = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH", "000858.SZ"]
            ticks = {code: get_mock_tick_data(code) for code in stock_codes}
            
            market_stats = {
                "total_stocks": len(stock_codes),
                "rising_count": random.randint(2, 4),
                "falling_count": random.randint(1, 3),
                "unchanged_count": 0,
                "rising_ratio": random.uniform(40, 80),
                "update_time": int(time.time() * 1000)
            }
            
            return create_response(
                data={
                    "market_stats": market_stats,
                    "ticks": ticks
                },
                count=len(stock_codes),
                message="获取模拟市场快照成功"
            )
            
    except Exception as e:
        logger.error(f"获取市场快照失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")

# 统计信息接口

@app.get("/api/v1/stats", response_model=Dict[str, Any])
async def get_server_stats():
    """获取服务器统计信息"""
    try:
        stats = {
            "server_status": "running",
            "start_time": int(time.time() * 1000),
            "data_manager_available": data_manager is not None,
            "rpc_client_available": rpc_client is not None,
            "api_version": "1.0.0"
        }
        
        if data_manager:
            if hasattr(data_manager, 'full_ticks'):
                stats["total_stocks"] = len(data_manager.full_ticks)
            if hasattr(data_manager, 'code_list'):
                stats["subscribed_stocks"] = len(data_manager.code_list)
        
        return create_response(
            data=stats,
            message="获取服务器统计成功"
        )
        
    except Exception as e:
        logger.error(f"获取服务器统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")

# 启动函数
def start_server(host: str = "0.0.0.0", port: int = 8000, 
                data_manager_instance=None, rpc_client_instance=None):
    """启动FastAPI服务器"""
    if data_manager_instance:
        set_data_manager(data_manager_instance)
    if rpc_client_instance:
        set_rpc_client(rpc_client_instance)
    
    logger.info(f"启动FastAPI服务器: http://{host}:{port}")
    logger.info(f"API文档地址: http://{host}:{port}/docs")
    
    uvicorn.run(app, host=host, port=port, log_level="info")

if __name__ == "__main__":
    # 直接运行服务器（使用模拟数据）
    start_server(host="0.0.0.0", port=8000)
