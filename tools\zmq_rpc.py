"""
高性能ZMQ RPC模块
支持异步请求响应模式，专为股票数据查询和交易指令优化
"""

import zmq
import json
import pickle
import msgpack
import uuid
import time
import threading
import logging
from typing import Dict, Any, Optional, Callable, Union
from dataclasses import asdict
from queue import Queue, Empty
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class ZMQRPCServer:
    """
    高性能ZMQ RPC服务器
    支持多线程处理和异步响应
    """
    
    def __init__(self, port: int = 5556, bind_address: str = "*",
                 serialization: str = "msgpack", worker_threads: int = 4):
        """
        初始化RPC服务器
        
        Args:
            port: 绑定端口
            bind_address: 绑定地址
            serialization: 序列化方式
            worker_threads: 工作线程数
        """
        self.port = port
        self.bind_address = bind_address
        self.serialization = serialization
        self.worker_threads = worker_threads
        
        # ZMQ上下文和socket
        self.context = zmq.Context()
        self.socket = None
        self.is_running = False
        
        # 方法注册
        self.methods: Dict[str, Callable] = {}
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=worker_threads)
        
        # 性能统计
        self.request_count = 0
        self.response_count = 0
        self.error_count = 0
        self.last_stats_time = time.time()
        
    def start(self):
        """启动RPC服务器"""
        if self.is_running:
            logger.warning("RPC Server already running")
            return
            
        try:
            self.socket = self.context.socket(zmq.REP)
            # 设置socket选项
            self.socket.setsockopt(zmq.RCVTIMEO, 1000)
            self.socket.setsockopt(zmq.SNDTIMEO, 1000)
            
            bind_addr = f"tcp://{self.bind_address}:{self.port}"
            self.socket.bind(bind_addr)
            self.is_running = True
            
            # 启动处理线程
            self.server_thread = threading.Thread(target=self._server_loop, daemon=True)
            self.server_thread.start()
            
            logger.info(f"ZMQ RPC Server started on {bind_addr}")
            
        except Exception as e:
            logger.error(f"Failed to start RPC server: {e}")
            raise
    
    def stop(self):
        """停止RPC服务器"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        if hasattr(self, 'server_thread'):
            self.server_thread.join(timeout=2.0)
            
        self.executor.shutdown(wait=True)
        
        if self.socket:
            self.socket.close()
        self.context.term()
        logger.info("ZMQ RPC Server stopped")
    
    def register_method(self, name: str, method: Callable):
        """
        注册RPC方法
        
        Args:
            name: 方法名
            method: 方法函数
        """
        self.methods[name] = method
        logger.info(f"Registered RPC method: {name}")
    
    def register_methods(self, methods: Dict[str, Callable]):
        """批量注册方法"""
        self.methods.update(methods)
        logger.info(f"Registered {len(methods)} RPC methods")
    
    def _serialize(self, data: Any) -> bytes:
        """序列化数据"""
        try:
            if self.serialization == "json":
                return json.dumps(data, ensure_ascii=False).encode('utf-8')
            elif self.serialization == "pickle":
                return pickle.dumps(data, protocol=pickle.HIGHEST_PROTOCOL)
            elif self.serialization == "msgpack":
                return msgpack.packb(data, use_bin_type=True)
            else:
                raise ValueError(f"Unsupported serialization: {self.serialization}")
        except Exception as e:
            logger.error(f"Serialization error: {e}")
            raise
    
    def _deserialize(self, data: bytes) -> Any:
        """反序列化数据"""
        try:
            if self.serialization == "json":
                return json.loads(data.decode('utf-8'))
            elif self.serialization == "pickle":
                return pickle.loads(data)
            elif self.serialization == "msgpack":
                return msgpack.unpackb(data, raw=False)
            else:
                raise ValueError(f"Unsupported serialization: {self.serialization}")
        except Exception as e:
            logger.error(f"Deserialization error: {e}")
            raise
    
    def _server_loop(self):
        """服务器主循环"""
        while self.is_running:
            try:
                # 接收请求
                message = self.socket.recv(zmq.NOBLOCK)
                request = self._deserialize(message)
                self.request_count += 1
                
                # 异步处理请求
                future = self.executor.submit(self._handle_request, request)
                response = future.result(timeout=30)  # 30秒超时
                
                # 发送响应
                response_data = self._serialize(response)
                self.socket.send(response_data)
                self.response_count += 1
                
            except zmq.Again:
                # 超时，继续循环
                continue
            except Exception as e:
                if self.is_running:
                    logger.error(f"Server loop error: {e}")
                    self.error_count += 1
                    # 发送错误响应
                    try:
                        error_response = {
                            "success": False,
                            "error": str(e),
                            "timestamp": time.time()
                        }
                        self.socket.send(self._serialize(error_response))
                    except:
                        pass
    
    def _handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个请求"""
        try:
            method_name = request.get("method")
            params = request.get("params", {})
            request_id = request.get("id")
            
            if method_name not in self.methods:
                return {
                    "success": False,
                    "error": f"Method '{method_name}' not found",
                    "id": request_id,
                    "timestamp": time.time()
                }
            
            # 调用方法
            method = self.methods[method_name]
            if isinstance(params, dict):
                result = method(**params)
            elif isinstance(params, list):
                result = method(*params)
            else:
                result = method(params)
            
            # 如果结果是dataclass，转换为字典
            if hasattr(result, '__dataclass_fields__'):
                result = asdict(result)
            
            return {
                "success": True,
                "result": result,
                "id": request_id,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Request handling error: {e}")
            return {
                "success": False,
                "error": str(e),
                "id": request.get("id"),
                "timestamp": time.time()
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        current_time = time.time()
        elapsed = current_time - self.last_stats_time
        
        stats = {
            "request_count": self.request_count,
            "response_count": self.response_count,
            "error_count": self.error_count,
            "requests_per_second": self.request_count / elapsed if elapsed > 0 else 0,
            "is_running": self.is_running,
            "registered_methods": list(self.methods.keys()),
            "worker_threads": self.worker_threads,
            "serialization": self.serialization
        }
        
        # 重置统计
        self.request_count = 0
        self.response_count = 0
        self.error_count = 0
        self.last_stats_time = current_time
        
        return stats


class ZMQRPCClient:
    """
    高性能ZMQ RPC客户端
    支持同步和异步调用
    """
    
    def __init__(self, server_address: str = "localhost", port: int = 5556,
                 serialization: str = "msgpack", timeout: int = 30):
        """
        初始化RPC客户端
        
        Args:
            server_address: 服务器地址
            port: 服务器端口
            serialization: 序列化方式
            timeout: 请求超时时间(秒)
        """
        self.server_address = server_address
        self.port = port
        self.serialization = serialization
        self.timeout = timeout * 1000  # 转换为毫秒
        
        # ZMQ上下文和socket
        self.context = zmq.Context()
        self.socket = None
        self.is_connected = False
        
        # 性能统计
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_stats_time = time.time()
        
    def connect(self):
        """连接到RPC服务器"""
        if self.is_connected:
            logger.warning("RPC Client already connected")
            return
            
        try:
            self.socket = self.context.socket(zmq.REQ)
            # 设置超时
            self.socket.setsockopt(zmq.RCVTIMEO, self.timeout)
            self.socket.setsockopt(zmq.SNDTIMEO, self.timeout)
            
            connect_addr = f"tcp://{self.server_address}:{self.port}"
            self.socket.connect(connect_addr)
            self.is_connected = True
            
            logger.info(f"ZMQ RPC Client connected to {connect_addr}")
            
        except Exception as e:
            logger.error(f"Failed to connect RPC client: {e}")
            raise
    
    def disconnect(self):
        """断开连接"""
        if not self.is_connected:
            return
            
        self.is_connected = False
        if self.socket:
            self.socket.close()
        self.context.term()
        logger.info("ZMQ RPC Client disconnected")
    
    def _serialize(self, data: Any) -> bytes:
        """序列化数据"""
        try:
            if self.serialization == "json":
                return json.dumps(data, ensure_ascii=False).encode('utf-8')
            elif self.serialization == "pickle":
                return pickle.dumps(data, protocol=pickle.HIGHEST_PROTOCOL)
            elif self.serialization == "msgpack":
                return msgpack.packb(data, use_bin_type=True)
            else:
                raise ValueError(f"Unsupported serialization: {self.serialization}")
        except Exception as e:
            logger.error(f"Serialization error: {e}")
            raise
    
    def _deserialize(self, data: bytes) -> Any:
        """反序列化数据"""
        try:
            if self.serialization == "json":
                return json.loads(data.decode('utf-8'))
            elif self.serialization == "pickle":
                return pickle.loads(data)
            elif self.serialization == "msgpack":
                return msgpack.unpackb(data, raw=False)
            else:
                raise ValueError(f"Unsupported serialization: {self.serialization}")
        except Exception as e:
            logger.error(f"Deserialization error: {e}")
            raise
    
    def call(self, method: str, params: Union[Dict, List, Any] = None) -> Any:
        """
        同步RPC调用
        
        Args:
            method: 方法名
            params: 参数
            
        Returns:
            调用结果
        """
        if not self.is_connected:
            raise RuntimeError("RPC Client not connected")
        
        request_id = str(uuid.uuid4())
        request = {
            "method": method,
            "params": params or {},
            "id": request_id,
            "timestamp": time.time()
        }
        
        try:
            # 发送请求
            request_data = self._serialize(request)
            self.socket.send(request_data)
            self.request_count += 1
            
            # 接收响应
            response_data = self.socket.recv()
            response = self._deserialize(response_data)
            
            if response.get("success"):
                self.success_count += 1
                return response.get("result")
            else:
                self.error_count += 1
                error_msg = response.get("error", "Unknown error")
                raise RuntimeError(f"RPC call failed: {error_msg}")
                
        except zmq.Again:
            self.error_count += 1
            raise TimeoutError(f"RPC call timeout after {self.timeout/1000}s")
        except Exception as e:
            self.error_count += 1
            logger.error(f"RPC call error: {e}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        current_time = time.time()
        elapsed = current_time - self.last_stats_time
        
        stats = {
            "request_count": self.request_count,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.success_count / self.request_count if self.request_count > 0 else 0,
            "requests_per_second": self.request_count / elapsed if elapsed > 0 else 0,
            "is_connected": self.is_connected,
            "serialization": self.serialization,
            "timeout": self.timeout / 1000
        }
        
        # 重置统计
        self.request_count = 0
        self.success_count = 0
        self.error_count = 0
        self.last_stats_time = current_time
        
        return stats
