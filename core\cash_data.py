from queue import Empty, Queue
import threading 
import os 
from .setting import SETTINGS, data_dir
from .utility import read_csv, get_stock_ratio
from .excel import ExcelReader
from .timeseries import TimeSeriesManager
import pandas as pd
    
class CashManager:
    name = "cash_manager"
    def __init__(self) -> None:
        # excle 输入的信息 sheet_name: 要求唯一
        self.out_info = {str: pd.DataFrame}
        self.queue = Queue()
        self.thread = threading.Thread(target=self.on_ticks)
        self.queue_dct: dict[str, Queue] = {}
        self.code_list = []
        self.base_df = None
        self.base_col_idx = {}
        self.full_ticks = {}
        self.active = False
        self.cash_manger = TimeSeriesManager()
    
    def update_initial_capacity(self, initial_capacity: int = 4800):
        self.cash_manger.initial_capacity = initial_capacity
    
    def on_init(self):
        # 读取统计信息文件
        stats_file = os.path.join(data_dir, SETTINGS["上一交易日"], "统计信息.xlsx")
        if not os.path.exists(stats_file):
            print(f"警告: 统计信息文件不存在: {stats_file}")
            return
        excle_reader = ExcelReader(stats_file)
        sheet_names = excle_reader.get_sheet_names()
        print(f"统计信息文件包含工作表: {sheet_names}")
        stock_list = []
        for name in sheet_names:
            df = excle_reader.read_sheet(name)
            self.out_info[name] = df
            if name.endswith("股票池"):
                stock_list.append(df["股票代码"])
        stock_list = list(set(stock_list))
        # 读取股票基本信息
        base_info_file = os.path.join(data_dir, "股票基本信息.xlsx")
        excle_reader = ExcelReader(base_info_file)
        base_df = excle_reader.read_sheet("股票")
        pre_day_df = self.out_info["昨日信息"]
        info_lst = []
        for index, row in base_df.iterrows():
            stock_code = row["股票代码"]
            limit_ratio = row["最大涨跌幅"]
            pre_close = pre_day_df.at[stock_code, "收盘价"]
            info_lst.append({
                "股票代码": stock_code,
                "股票名称": row["股票名称"],
                "流通股本": row["流通股本"],
                "总股本": row["总股本"],
                "最大涨跌幅": limit_ratio,
                "涨停价": round((1 + limit_ratio)* pre_close, 2),
                "跌停价": round((1 - limit_ratio)* pre_close, 2),
                "昨日收盘价": pre_close,
                "昨日最高价": pre_day_df.at[stock_code, "最高价"],
                "昨日最低价": pre_day_df.at[stock_code, "最低价"],
                "昨日开盘价": pre_day_df.at[stock_code, "开盘价"],
                "昨日成交量": pre_day_df.at[stock_code, "成交量"],
                "昨日成交额": pre_day_df.at[stock_code, "成交额"],

            })
        self.base_df = pd.DataFrame(info_lst)
        self.base_col_idx = { col: i for i, col in enumerate(self.base_df.columns) }
        self.cash_manger.add_stocks(stock_list)
        self.code_list = stock_list
        print(f"去重后所有股票池共有 {len(stock_list)} 个股票代码...")
    
    def add_queue(self, queue: Queue, key: str):
        if key in self.queue_dct:
            return
        self.queue_dct[key] = queue

    def remove_queue(self, key: str):
        if key in self.queue_dct:
            del self.queue_dct[key]
    
    def handle_tick(self, data: dict) -> None:
        if not data:
            return
        for code , d in data.items():
            if code not in self.code_list:
                continue
            # 取出竞价数据 9:25的保留
            if d['open_interest'] == 12:
                continue
            bp_data: list = d["bidPrice"]
            ap_data: list = d["askPrice"]
            bv_data: list = d["bidVol"]
            av_data: list = d["askVol"]
            new_dct = {
                "timestamp": d["time"],
                "stock_code": code,
                "last_price": d["lastPrice"],
                "open": d["open"],
                "high": d["high"],
                "low": d["low"],
                "pre_close": d["lastClose"],
                "volume": d["volume"],
                "amount": d["amount"],
                "bid1_price": bp_data[0],
                "bid2_price": bp_data[1],
                "bid3_price": bp_data[2],
                "bid4_price": bp_data[3],
                "bid5_price": bp_data[4],
                "ask1_price": ap_data[0],
                "ask2_price": ap_data[1],
                "ask3_price": ap_data[2],
                "ask4_price": ap_data[3],
                "ask5_price": ap_data[4],
                "bid1_vol": bv_data[0],
                "bid2_vol": bv_data[1],
                "bid3_vol": bv_data[2],
                "bid4_vol": bv_data[3],
                "bid5_vol": bv_data[4],
                "ask1_vol": av_data[0],
                "ask2_vol": av_data[1],
                "ask3_vol": av_data[2],
                "ask4_vol": av_data[3],
                "ask5_vol": av_data[4],
                "change": round((d["lastPrice"] - d["preClose"]) / d["preClose"], 4) * 100,
                "is_yang": False,
                "is_yin": False,
            }
            pre_tick = self.full_ticks.get(code, None)
            if pre_tick:
                new_dct["net_volume"] = new_dct["volume"] - pre_tick["volume"]
                new_dct["net_amount"] = new_dct["amount"] - pre_tick["amount"]
                if new_dct["last_price"] > pre_tick["last_price"]:
                    new_dct["is_yang"] = True
                else:
                    new_dct["is_yin_volume"] = True
                
                new_dct["limit_ratio"] = pre_tick["limit_ratio"]
                new_dct["limit_up_price"] = pre_tick["limit_up_price"]
                new_dct["limit_down_price"] = pre_tick["limit_down_price"]
                new_dct["float_volume"] = pre_tick["float_volume"]

            else:
                limit_ratio = get_stock_ratio(code)
                row_df = self.base_df.at[code]
                pre_close = new_dct["pre_close"] 
                new_dct["net_volume"] = 0
                new_dct["net_amount"] = 0
                new_dct["limit_ratio"] = limit_ratio
                new_dct["limit_up_price"] = round(pre_close * (1 + limit_ratio), 2)
                new_dct["limit_down_price"] = round(pre_close * (1 - limit_ratio), 2)
                new_dct["float_volume"] = row_df["流通股本"]
            self.full_ticks[code] = new_dct
            self.cash_manger.add_stock_data(code, new_dct)

    def on_ticks(self) -> None:
        while self.active:
            try:
                data = self.queue.get(timeout=0.5)
                self.handle_tick(data)
            except Empty:
                continue

    def start(self):
        self.active = True
        self.thread.start()

    def close(self):
        self.active = False
        self.thread.join()

    