from queue import Queue, Empty
from datetime import datetime
from collections.abc import Callable

import taosws
import pandas as pd


from .object import TickData

from .setting import SETTINGS


class TaosDatabase:
    """TDengine数据库接口"""
    name = "taos_database"
    def __init__(self) -> None:
        """构造函数"""
        self.queue_dct: dict[str, Queue] = {}
        self.user: str = None
        self.password: str = None
        self.host: str = None
        self.port: int = None
        self.timezone: str = None
        self.db_name: str = None
        # 缓存字典
        # 连接数据库
        self.conn: taosws.TaosConnection = None
        self.cursor: taosws.TaosCursor = None
        self.tick_keys = ["vt_symbol", "datetime", "last_price", "open", "high", "low", 
            "pre_close", "volume", "amount", "open_interest", "transaction_num",
            "bid_price_1", "bid_price_2", "bid_price_3", "bid_price_4", "bid_price_5",
            "ask_price_1", "ask_price_2", "ask_price_3", "ask_price_4", "ask_price_5",
            "bid_volume_1", "bid_volume_2", "bid_volume_3", "bid_volume_4", "bid_volume_5",
            "ask_volume_1", "ask_volume_2", "ask_volume_3", "ask_volume_4", "ask_volume_5",
            "localtime"
            ]

    def connect(self, setting: dict=SETTINGS) -> bool:
        """连接数据库"""
        self.user = SETTINGS["database.user"]
        self.password = SETTINGS["database.password"]
        self.host = SETTINGS["database.host"]
        self.port = SETTINGS["database.port"]
        self.timezone= SETTINGS["database.timezone"]
        self.db_name = SETTINGS["database.database"]
        try:
            self.conn = taosws.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                port=self.port,
                database=self.db_name,
                timezone=self.timezone
            )
            self.cursor = self.conn.cursor()
            return True
        except Exception as e:
            print(f"连接数据库失败: {e}")
            return False


    def add_queue(self, queue: Queue, key: str):
        if key in self.queue_dct:
            return
        self.queue_dct[key] = queue

    def remove_queue(self, key: str):
        if key in self.queue_dct:
            del self.queue_dct[key]
    
    def subscribe_featch(self, start_time: datetime, end_time: datetime):
        """订阅行情"""
        start_time = int(start_time.timestamp() * 1000)
        end_time = int(end_time.timestamp() * 1000)
        c = 0
        while True:
            st = start_time + c * 30*1000
            et = st + 30 * 1000
            if et > end_time:
                break
            c += 1
            sql = f"select {','.join(self.tick_keys)} from {self.db_name}.tick where datetime >= {st} and datetime < {et}"
            self.cursor.execute(sql)
            data = self.cursor.fetchall()
            ticks = []
            for row in data:
                tick = TickData(
                    vt_symbol=row[0],
                    datetime=row[1],
                    timestamp=int(row[1].timestamp() * 1000),
                    last_price=row[2],
                    open=row[3],
                    high=row[4],
                    low=row[5],
                    pre_close=row[6],
                    volume=row[7],
                    amount=row[8],
                    open_interest=row[9],
                    transaction_num=row[10],
                    bid_price_1=row[11],
                    bid_price_2=row[12],
                    bid_price_3=row[13],
                    bid_price_4=row[14],
                    bid_price_5=row[15],
                    ask_price_1=row[16],
                    ask_price_2=row[17],
                    ask_price_3=row[18],
                    ask_price_4=row[19],
                    ask_price_5=row[20],
                    bid_volume_1=row[21],
                    bid_volume_2=row[22],
                    bid_volume_3=row[23],
                    bid_volume_4=row[24],
                    bid_volume_5=row[25],
                    ask_volume_1=row[26],
                    ask_volume_2=row[27],
                    ask_volume_3=row[28],
                    ask_volume_4=row[29],
                    ask_volume_5=row[30],
                )
                ticks.append(tick)
            for queue in self.queue_dct.values():
                queue.put(ticks)

    def subscribe(self,topic_name: str, code_list: list[str]):
        """订阅行情"""
        conf = self.get_sub_config(topic_name, auto_offset_reset="earliest")
        consumer = taosws.Consumer(conf)
        consumer.subscribe([topic_name])
        
        try:
            while True:
                # 拉取消息（超时1秒）
                message = consumer.poll(timeout=1.0)
                if not message:
                    continue
                ticks = []
                for block in message:
                    for row in block.fetchall():
                        tick = TickData(
                            vt_symbol=row[0],
                            datetime=row[1],
                            last_price=row[2],
                            open=row[3],
                            high=row[4],
                            low=row[5],
                            pre_close=row[6],
                            volume=row[7],
                            amount=row[8],
                            open_interest=row[9],
                            transaction_num=row[10],
                            bid_price_1=row[11],
                            bid_price_2=row[12],
                            bid_price_3=row[13],
                            bid_price_4=row[14],
                            bid_price_5=row[15],
                            ask_price_1=row[16],
                            ask_price_2=row[17],
                            ask_price_3=row[18],
                            ask_price_4=row[19],
                            ask_price_5=row[20],
                            bid_volume_1=row[21],
                            bid_volume_2=row[22],
                            bid_volume_3=row[23],
                            bid_volume_4=row[24],
                            bid_volume_5=row[25],
                            ask_volume_1=row[26],
                            ask_volume_2=row[27],
                            ask_volume_3=row[28],
                            ask_volume_4=row[29],
                            ask_volume_5=row[30],
                        )
                        ticks.append(tick)
                if ticks:
                    for queue in self.queue_dct.values():
                        queue.put(ticks)
                consumer.commit(message)
        except Exception as e:
            print(f"消费过程中出错: {e}")
        finally:
            # 清理资源
            consumer.unsubscribe()
            consumer.close()

    def get_sub_config(self, group_id: str, auto_offset_reset: str = "latest"):
        """获取订阅配置"""
        conf = {
            "td.connect.websocket.scheme": "ws",
            "td.connect.ip": self.host,           # 服务器地址
            "td.connect.port": self.port,              # WebSocket端口
            "td.connect.user": self.user,              # 用户名
            "td.connect.pass": self.password,          # 密码
            # "td.connect.timezone": self.timezone,          # 密码
            "group.id": "tock_1",
            "auto.offset.reset": auto_offset_reset,
        }
        return conf
    

    def create_tick_topic(self, topic_name: str, start_time: datetime, end_time: datetime):
        """创建tick主题"""
        start_time = int(start_time.timestamp() * 1000)
        end_time = int(end_time.timestamp() * 1000)
        sql = f"CREATE TOPIC {topic_name} AS  select {','.join(self.tick_keys)} from {self.db_name}.tick where datetime >= {start_time} and datetime <= {end_time}"
        self.conn.execute(sql)

    def drop_tick_topic(self, topic_name="tick"):
        """删除tick主题"""
        sql = f"DROP TOPIC {topic_name}"
        self.conn.execute(sql)


        def __exit__(self, exc_type, exc_value, traceback):
            # ⭐ 无论是否发生异常，这里都会执行
            # 如果你想让异常继续向上抛，返回 False
            # 如果你想吞掉异常，返回 True
            return False  # 不吞异常



