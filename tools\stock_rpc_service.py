"""
股票数据RPC服务模块
提供股票数据查询、历史数据获取、交易指令等RPC服务
"""

import time
import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import numpy as np

from .zmq_rpc import ZMQRPCServer, ZMQRPCClient
from .stock_data_bridge import StockTickData

logger = logging.getLogger(__name__)


class StockDataRPCService:
    """
    股票数据RPC服务
    提供数据查询和交易相关的RPC接口
    """
    
    def __init__(self, port: int = 5556, data_manager=None, trader=None):
        """
        初始化RPC服务
        
        Args:
            port: 服务端口
            data_manager: 数据管理器
            trader: 交易接口
        """
        self.server = ZMQRPCServer(port=port, worker_threads=8)
        self.data_manager = data_manager
        self.trader = trader
        
        # 注册RPC方法
        self._register_methods()
        
        # 数据缓存
        self.tick_cache: Dict[str, StockTickData] = {}
        self.cache_expire_time = 5  # 缓存过期时间(秒)
        
    def start(self):
        """启动RPC服务"""
        self.server.start()
        logger.info("Stock Data RPC Service started")
    
    def stop(self):
        """停止RPC服务"""
        self.server.stop()
        logger.info("Stock Data RPC Service stopped")
    
    def _register_methods(self):
        """注册RPC方法"""
        methods = {
            # 数据查询
            "get_latest_tick": self.get_latest_tick,
            "get_latest_ticks": self.get_latest_ticks,
            "get_market_snapshot": self.get_market_snapshot,
            "get_historical_data": self.get_historical_data,
            "get_stock_info": self.get_stock_info,
            "search_stocks": self.search_stocks,
            
            # 技术指标
            "calculate_ma": self.calculate_ma,
            "calculate_macd": self.calculate_macd,
            "calculate_rsi": self.calculate_rsi,
            "calculate_bollinger": self.calculate_bollinger,
            
            # 交易相关
            "place_order": self.place_order,
            "cancel_order": self.cancel_order,
            "get_positions": self.get_positions,
            "get_account_info": self.get_account_info,
            "get_orders": self.get_orders,
            
            # 系统状态
            "get_server_status": self.get_server_status,
            "get_performance_stats": self.get_performance_stats,
            "ping": self.ping
        }
        
        self.server.register_methods(methods)
    
    # ==================== 数据查询方法 ====================
    
    def get_latest_tick(self, stock_code: str) -> Dict[str, Any]:
        """获取最新tick数据"""
        try:
            if self.data_manager and hasattr(self.data_manager, 'full_ticks'):
                tick_data = self.data_manager.full_ticks.get(stock_code)
                if tick_data:
                    return {
                        "success": True,
                        "data": tick_data,
                        "timestamp": time.time()
                    }
            
            return {
                "success": False,
                "error": f"No data found for {stock_code}",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting latest tick for {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def get_latest_ticks(self, stock_codes: List[str] = None) -> Dict[str, Any]:
        """获取多个股票的最新tick数据"""
        try:
            if not self.data_manager or not hasattr(self.data_manager, 'full_ticks'):
                return {
                    "success": False,
                    "error": "Data manager not available",
                    "timestamp": time.time()
                }
            
            if stock_codes is None:
                # 返回所有数据
                return {
                    "success": True,
                    "data": self.data_manager.full_ticks,
                    "count": len(self.data_manager.full_ticks),
                    "timestamp": time.time()
                }
            else:
                # 返回指定股票数据
                result = {}
                for code in stock_codes:
                    if code in self.data_manager.full_ticks:
                        result[code] = self.data_manager.full_ticks[code]
                
                return {
                    "success": True,
                    "data": result,
                    "count": len(result),
                    "timestamp": time.time()
                }
                
        except Exception as e:
            logger.error(f"Error getting latest ticks: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def get_market_snapshot(self) -> Dict[str, Any]:
        """获取市场快照"""
        try:
            if not self.data_manager:
                return {
                    "success": False,
                    "error": "Data manager not available",
                    "timestamp": time.time()
                }
            
            # 获取所有tick数据
            all_ticks = getattr(self.data_manager, 'full_ticks', {})
            
            # 计算市场统计
            total_stocks = len(all_ticks)
            rising_count = 0
            falling_count = 0
            unchanged_count = 0
            
            for tick in all_ticks.values():
                if tick.get('last_price', 0) > tick.get('pre_close', 0):
                    rising_count += 1
                elif tick.get('last_price', 0) < tick.get('pre_close', 0):
                    falling_count += 1
                else:
                    unchanged_count += 1
            
            return {
                "success": True,
                "data": {
                    "total_stocks": total_stocks,
                    "rising_count": rising_count,
                    "falling_count": falling_count,
                    "unchanged_count": unchanged_count,
                    "rising_ratio": rising_count / total_stocks if total_stocks > 0 else 0,
                    "ticks": all_ticks
                },
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting market snapshot: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def get_historical_data(self, stock_code: str, period: str = "1d", 
                           count: int = 100) -> Dict[str, Any]:
        """获取历史数据"""
        try:
            if not self.data_manager or not hasattr(self.data_manager, 'cash_manger'):
                return {
                    "success": False,
                    "error": "Data manager not available",
                    "timestamp": time.time()
                }
            
            # 从时间序列管理器获取数据
            ts_manager = self.data_manager.cash_manger
            if hasattr(ts_manager, 'get_stock_data'):
                df = ts_manager.get_stock_data(stock_code, count)
                if df is not None and not df.empty:
                    return {
                        "success": True,
                        "data": df.to_dict('records'),
                        "count": len(df),
                        "timestamp": time.time()
                    }
            
            return {
                "success": False,
                "error": f"No historical data found for {stock_code}",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting historical data for {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def get_stock_info(self, stock_code: str) -> Dict[str, Any]:
        """获取股票基本信息"""
        try:
            if not self.data_manager or not hasattr(self.data_manager, 'base_df'):
                return {
                    "success": False,
                    "error": "Data manager not available",
                    "timestamp": time.time()
                }
            
            base_df = self.data_manager.base_df
            if base_df is not None and stock_code in base_df.index:
                stock_info = base_df.loc[stock_code].to_dict()
                return {
                    "success": True,
                    "data": stock_info,
                    "timestamp": time.time()
                }
            
            return {
                "success": False,
                "error": f"No info found for {stock_code}",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting stock info for {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def search_stocks(self, keyword: str, limit: int = 20) -> Dict[str, Any]:
        """搜索股票"""
        try:
            if not self.data_manager or not hasattr(self.data_manager, 'code_list'):
                return {
                    "success": False,
                    "error": "Data manager not available",
                    "timestamp": time.time()
                }
            
            code_list = self.data_manager.code_list
            # 简单的关键词匹配
            matched_codes = [code for code in code_list if keyword.upper() in code.upper()]
            
            return {
                "success": True,
                "data": matched_codes[:limit],
                "count": len(matched_codes),
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error searching stocks with keyword {keyword}: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    # ==================== 技术指标计算 ====================
    
    def calculate_ma(self, stock_code: str, period: int = 20) -> Dict[str, Any]:
        """计算移动平均线"""
        try:
            hist_data = self.get_historical_data(stock_code, count=period*2)
            if not hist_data["success"]:
                return hist_data
            
            df = pd.DataFrame(hist_data["data"])
            if "last_price" in df.columns:
                ma = df["last_price"].rolling(window=period).mean().iloc[-1]
                return {
                    "success": True,
                    "data": {"ma": ma, "period": period},
                    "timestamp": time.time()
                }
            
            return {
                "success": False,
                "error": "Price data not available",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error calculating MA for {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def calculate_macd(self, stock_code: str, fast: int = 12, slow: int = 26, 
                      signal: int = 9) -> Dict[str, Any]:
        """计算MACD指标"""
        try:
            hist_data = self.get_historical_data(stock_code, count=slow*3)
            if not hist_data["success"]:
                return hist_data
            
            df = pd.DataFrame(hist_data["data"])
            if "last_price" in df.columns:
                prices = df["last_price"]
                ema_fast = prices.ewm(span=fast).mean()
                ema_slow = prices.ewm(span=slow).mean()
                macd_line = ema_fast - ema_slow
                signal_line = macd_line.ewm(span=signal).mean()
                histogram = macd_line - signal_line
                
                return {
                    "success": True,
                    "data": {
                        "macd": macd_line.iloc[-1],
                        "signal": signal_line.iloc[-1],
                        "histogram": histogram.iloc[-1]
                    },
                    "timestamp": time.time()
                }
            
            return {
                "success": False,
                "error": "Price data not available",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error calculating MACD for {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def calculate_rsi(self, stock_code: str, period: int = 14) -> Dict[str, Any]:
        """计算RSI指标"""
        try:
            hist_data = self.get_historical_data(stock_code, count=period*3)
            if not hist_data["success"]:
                return hist_data
            
            df = pd.DataFrame(hist_data["data"])
            if "last_price" in df.columns:
                prices = df["last_price"]
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                
                return {
                    "success": True,
                    "data": {"rsi": rsi.iloc[-1], "period": period},
                    "timestamp": time.time()
                }
            
            return {
                "success": False,
                "error": "Price data not available",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error calculating RSI for {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def calculate_bollinger(self, stock_code: str, period: int = 20, 
                           std_dev: float = 2.0) -> Dict[str, Any]:
        """计算布林带指标"""
        try:
            hist_data = self.get_historical_data(stock_code, count=period*2)
            if not hist_data["success"]:
                return hist_data
            
            df = pd.DataFrame(hist_data["data"])
            if "last_price" in df.columns:
                prices = df["last_price"]
                sma = prices.rolling(window=period).mean()
                std = prices.rolling(window=period).std()
                upper_band = sma + (std * std_dev)
                lower_band = sma - (std * std_dev)
                
                return {
                    "success": True,
                    "data": {
                        "upper_band": upper_band.iloc[-1],
                        "middle_band": sma.iloc[-1],
                        "lower_band": lower_band.iloc[-1],
                        "period": period,
                        "std_dev": std_dev
                    },
                    "timestamp": time.time()
                }
            
            return {
                "success": False,
                "error": "Price data not available",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands for {stock_code}: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    # ==================== 交易相关方法 ====================
    
    def place_order(self, stock_code: str, action: str, quantity: int, 
                   price: float = None, order_type: str = "limit") -> Dict[str, Any]:
        """下单"""
        try:
            if not self.trader:
                return {
                    "success": False,
                    "error": "Trader not available",
                    "timestamp": time.time()
                }
            
            # 调用交易接口
            if hasattr(self.trader, 'order_stock'):
                result = self.trader.order_stock(
                    stock_code=stock_code,
                    action=action,
                    quantity=quantity,
                    price=price,
                    order_type=order_type
                )
                return {
                    "success": True,
                    "data": result,
                    "timestamp": time.time()
                }
            
            return {
                "success": False,
                "error": "Order method not available",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """撤单"""
        try:
            if not self.trader:
                return {
                    "success": False,
                    "error": "Trader not available",
                    "timestamp": time.time()
                }
            
            if hasattr(self.trader, 'cancel_order'):
                result = self.trader.cancel_order(order_id)
                return {
                    "success": True,
                    "data": result,
                    "timestamp": time.time()
                }
            
            return {
                "success": False,
                "error": "Cancel method not available",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error canceling order: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def get_positions(self) -> Dict[str, Any]:
        """获取持仓"""
        try:
            if not self.trader:
                return {
                    "success": False,
                    "error": "Trader not available",
                    "timestamp": time.time()
                }
            
            if hasattr(self.trader, 'get_positions'):
                positions = self.trader.get_positions()
                return {
                    "success": True,
                    "data": positions,
                    "timestamp": time.time()
                }
            
            return {
                "success": False,
                "error": "Positions method not available",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            if not self.trader:
                return {
                    "success": False,
                    "error": "Trader not available",
                    "timestamp": time.time()
                }
            
            if hasattr(self.trader, 'get_account_info'):
                account_info = self.trader.get_account_info()
                return {
                    "success": True,
                    "data": account_info,
                    "timestamp": time.time()
                }
            
            return {
                "success": False,
                "error": "Account info method not available",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def get_orders(self, status: str = "all") -> Dict[str, Any]:
        """获取订单列表"""
        try:
            if not self.trader:
                return {
                    "success": False,
                    "error": "Trader not available",
                    "timestamp": time.time()
                }
            
            if hasattr(self.trader, 'get_orders'):
                orders = self.trader.get_orders(status)
                return {
                    "success": True,
                    "data": orders,
                    "timestamp": time.time()
                }
            
            return {
                "success": False,
                "error": "Orders method not available",
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting orders: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    # ==================== 系统状态方法 ====================
    
    def get_server_status(self) -> Dict[str, Any]:
        """获取服务器状态"""
        return {
            "success": True,
            "data": {
                "status": "running",
                "uptime": time.time(),
                "data_manager_available": self.data_manager is not None,
                "trader_available": self.trader is not None
            },
            "timestamp": time.time()
        }
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = self.server.get_stats()
        return {
            "success": True,
            "data": stats,
            "timestamp": time.time()
        }
    
    def ping(self) -> Dict[str, Any]:
        """心跳检测"""
        return {
            "success": True,
            "data": "pong",
            "timestamp": time.time()
        }


class StockDataRPCClient:
    """
    股票数据RPC客户端
    提供便捷的RPC调用接口
    """
    
    def __init__(self, server_address: str = "localhost", port: int = 5556):
        """
        初始化RPC客户端
        
        Args:
            server_address: 服务器地址
            port: 服务器端口
        """
        self.client = ZMQRPCClient(server_address, port)
        
    def connect(self):
        """连接到服务器"""
        self.client.connect()
    
    def disconnect(self):
        """断开连接"""
        self.client.disconnect()
    
    def get_latest_tick(self, stock_code: str) -> Dict[str, Any]:
        """获取最新tick数据"""
        return self.client.call("get_latest_tick", {"stock_code": stock_code})
    
    def get_latest_ticks(self, stock_codes: List[str] = None) -> Dict[str, Any]:
        """获取多个股票的最新tick数据"""
        return self.client.call("get_latest_ticks", {"stock_codes": stock_codes})
    
    def get_market_snapshot(self) -> Dict[str, Any]:
        """获取市场快照"""
        return self.client.call("get_market_snapshot")
    
    def get_historical_data(self, stock_code: str, period: str = "1d", 
                           count: int = 100) -> Dict[str, Any]:
        """获取历史数据"""
        return self.client.call("get_historical_data", {
            "stock_code": stock_code,
            "period": period,
            "count": count
        })
    
    def place_order(self, stock_code: str, action: str, quantity: int, 
                   price: float = None, order_type: str = "limit") -> Dict[str, Any]:
        """下单"""
        return self.client.call("place_order", {
            "stock_code": stock_code,
            "action": action,
            "quantity": quantity,
            "price": price,
            "order_type": order_type
        })
    
    def get_positions(self) -> Dict[str, Any]:
        """获取持仓"""
        return self.client.call("get_positions")
    
    def ping(self) -> Dict[str, Any]:
        """心跳检测"""
        return self.client.call("ping")
