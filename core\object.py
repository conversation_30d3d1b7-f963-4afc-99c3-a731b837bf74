"""
Basic data structure used for general trading function in the trading platform.
"""

from dataclasses import dataclass, field
from datetime import datetime as Datetime

@dataclass
class StockData:
    pass

@dataclass(slots=True)
class TickData:
    #   stop_status: int = 0 # 0: 1:涨停 -1：跌停  2: 卖一价等于涨停价(涨停前)
    vt_symbol: str
    datetime: Datetime = None
    timestamp: int = 0
    volume: float = 0
    amount: float = 0
    open_interest: float = 0
    last_price: float = 0


    open: float = 0
    high: float = 0
    low: float = 0
    pre_close: float = 0
    transaction_num: int = 0

    bid_price_1: float = 0
    bid_price_2: float = 0
    bid_price_3: float = 0
    bid_price_4: float = 0
    bid_price_5: float = 0

    ask_price_1: float = 0
    ask_price_2: float = 0
    ask_price_3: float = 0
    ask_price_4: float = 0
    ask_price_5: float = 0

    bid_volume_1: float = 0
    bid_volume_2: float = 0
    bid_volume_3: float = 0
    bid_volume_4: float = 0
    bid_volume_5: float = 0

    ask_volume_1: float = 0
    ask_volume_2: float = 0
    ask_volume_3: float = 0
    ask_volume_4: float = 0
    ask_volume_5: float = 0

    net_volume: float = 0
    net_amount: float = 0
    stop_status: int = 0
    # 涨跌幅
    change: float = 0
    max_bid_volume_1: float = 0
    max_ask_volume_1: float = 0
    max_bid_price_1: float = 0
    max_ask_price_1: float = 0
    yang_volume: float = 0
    yin_volume: float = 0
    yang_amount: float = 0
    yin_amount: float = 0
    index: int = -1
 
