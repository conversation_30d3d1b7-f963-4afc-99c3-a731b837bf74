df = pd.DataFrame({
    'price': [...],
    'volume': [...]
}, index=pd.DatetimeIndex([...]))  # 或者 'timestamp' 列已排序

时间列（或索引）是 单调递增 的。
你需要极致性能（如高频回测、实时处理）。

取指定时间的整行（时间不一定存在
    目标：找第一个 >= target_time 的行（类似“下一个时间点”）

    ✅ 最快方式：searchsorted + iloc
    为什么快？ O(log n)，二分查找，不生成布尔数组。
    # 如果时间在 index 上
    idx = df.index.searchsorted(target_time, side='left')
    if idx < len(df):
        row = df.iloc[idx]  # Series
        # 或 df.iloc[[idx]] 返回 DataFrame
    else:
        row = None

    # 如果时间在列中（如 df['timestamp']）
    timestamps = df['timestamp'].values
    idx = np.searchsorted(timestamps, target_time, side='left')
    if idx < len(df):
        row = df.iloc[idx]

取固定 idx 的整行（已知位置）
目标：idx = 100，取第 100 行

    ✅ 最快方式：iloc[idx]
    row = df.iloc[100]        # 返回 Series
    # 或
    row = df.iloc      # 返回 DataFrame（保持二维结构）
    性能：O(1) 随机访问，这是最快的方式，无需其他方法。

3️⃣ 取指定行（idx）的指定单列
    目标：取第 idx 行，'price' 列的值
    ✅ 最快方式：iat（整数位置）或 at（标签）
    ✅ 最快方式：iat（整数位置）或 at（标签）
    # 方法1：iat（最快，推荐）
    value = df.iat[idx, df.columns.get_loc('price')]
    # 方法2：先提取列，再用 iloc（也很快）
    price_col = df['price'].values  # 提前提取为 numpy array
    value = price_col[idx]
    📌 iat 是最快的标量访问方式，但需配合 get_loc 获取列位置。

    ⚠️ 避免用 df.iloc[idx]['price'] —— 会创建临时 Series，慢

4️⃣ 取索引范围 idx1 到 idx2 的一列或多列
目标：idx1:idx2 行，['price', 'volume'] 列
     最快方式：iloc + 切片
     subset = df.iloc[idx1:idx2+1, :]                    # 所有列
    subset = df.iloc[idx1:idx2+1, [0, 1]]               # 指定列位置
    subset = df.iloc[idx1:idx2+1, df.columns.isin(['price', 'volume'])]  # 按列名
    iloc 切片是 O(k)（k 是范围长度），非常高效。

    ⚠️ 避免用布尔索引如 df[(idx1 <= index) & (index <= idx2)] —— O(n)

5️⃣ 取多个指定时间对应行的指定一列或多列
目标：给定时间列表 [t1, t2, t3]，取每行的 'price' 或多列

✅ 最快方式：批量 searchsorted + iloc
    target_times = [t1, t2, t3]
    timestamps = df.index.values  # 或 df['timestamp'].values

    # 批量查找每个时间的插入位置
    indices = np.searchsorted(timestamps, target_times, side='left')

    # 过滤有效索引（防止越界）
    valid = indices < len(df)
    indices = indices[valid]

    # 一次性取值
    result = df.iloc[indices][['price', 'volume']]  # 多列
    # 或
    prices = df['price'].values[indices]            # 单列，最快
    📌 优势：批量二分查找，避免循环调用 searchsorted

总结：最快方法速查表
需求	最快方法	代码示例
指定时间整行（时间不一定存在）	searchsorted + iloc	idx = df.index.searchsorted(t); df.iloc[idx]
固定 idx 整行	iloc[idx]	df.iloc[100]
指定行 idx + 指定单列	iat 或 values[idx]	df.iat[idx, col_idx] 或 df['col'].values[idx]
idx1 到 idx2 范围 + 一列/多列	iloc[slice]	df.iloc[idx1:idx2+1, cols]
多个时间 + 指定列	批量 searchsorted + iloc	indices = np.searchsorted(ts, times); df.iloc[indices][cols]


性能优化建议
提前提取列到 numpy.array：
python
深色版本
price_arr = df['price'].values  # 避免重复访问 pd.Series
确保索引/列已排序：searchsorted 才能用。
避免链式索引：如 df[...][...]，用 loc/iloc 一步到位。
批量操作优于循环：尽量向量化。

col_map = {'price': 0, 'volume': 1, 'amount': 2}
%timeit col_map['price']
# 示例输出：~0.05~0.1 μs（比 get_loc 快 10~20 倍）