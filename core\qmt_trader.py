from xtquant import xtconstant
from xtquant.xttrader import XtQuantTrader, XtQuantTraderCallback
from xtquant.xttype import StockAccount
import logging

logger = logging.getLogger(__name__)


class MyXtQuantTraderCallback(XtQuantTraderCallback):
    def on_disconnected(self):
        """
        连接断开
        :return:
        """
        print("connection lost")

    def on_stock_order(self, order):
        """
        委托回报推送
        :param order: XtOrder对象
        :return:
        """
        print("on order callback:")
        print(order.stock_code, order.order_status, order.order_sysid)

    def on_stock_asset(self, asset):
        """
        资金变动推送
        :param asset: XtAsset对象
        :return:
        """
        print("on asset callback")
        print(asset.account_id, asset.cash, asset.total_asset)

    def on_stock_trade(self, trade):
        """
        成交变动推送
        :param trade: XtTrade对象
        :return:
        """
        print("on trade callback")
        print(trade.account_id, trade.stock_code, trade.order_id)

    def on_stock_position(self, position):
        """
        持仓变动推送
        :param position: XtPosition对象
        :return:
        """
        print("on position callback")
        print(position.stock_code, position.volume)

    def on_order_error(self, order_error):
        """
        委托失败推送
        :param order_error:XtOrderError 对象
        :return:
        """
        print("on order_error callback")
        print(order_error.order_id, order_error.error_id, order_error.error_msg)

    def on_cancel_error(self, cancel_error):
        """
        撤单失败推送
        :param cancel_error: XtCancelError 对象
        :return:
        """
        print("on cancel_error callback")
        print(cancel_error.order_id, cancel_error.error_id, cancel_error.error_msg)

    def on_order_stock_async_response(self, response):
        """
        异步下单回报推送
        :param response: XtOrderResponse 对象
        :return:
        """
        print("on_order_stock_async_response")
        print(response.account_id, response.order_id, response.seq)


class QmtTrader:
    def __init__(self):
        self.path = None
        self.session_id = None
        self.account = None
        self.account_type = None
        self.acc = None
        self.xt_trader = None
        self.is_run = False

    def connect(self, setting: dict):
        self.path = setting["qmt.path"]
        self.session_id = setting["qmt.session_id"]
        self.account = setting["qmt.access"]
        self.account_type = setting.get("qmt.account_type", "STOCK")
        if self.is_run:
            return
        logger.info(f"path: {self.path}, session_id: {self.session_id}")
        # 连接客户端
        self.xt_trader = XtQuantTrader(self.path, self.session_id)
        # 连接资金账户
        logger.info(f"account: {self.account}, account_type: {self.account_type}")
        self.acc = StockAccount(account_id=self.account, account_type=self.account_type)
        # 创建交易回调类对象，并声明接收回调
        callback = MyXtQuantTraderCallback()
        self.xt_trader.register_callback(callback)
        # 启动交易线程
        self.xt_trader.start()
        # 建立交易连接，返回0表示连接成功
        connect_result = self.xt_trader.connect()
        if connect_result == 0:
            logger.info("Qmt连接成功")
            # 对交易回调进行订阅，订阅后可以收到交易主推，返回0表示订阅成功
            code = self.xt_trader.subscribe(self.acc)
            if code == 0:
                self.is_run = True
                logger.info("交易回调进行订阅成功")
            else:
                logger.error("交易回调进行订阅失败")
        else:
            self.xt_trader = None
            logger.error("Qmt连接失败")

    def buy(self, stock_code, order_type=xtconstant.STOCK_BUY,
            order_volume=100, price_type=xtconstant.FIX_PRICE, price=20, strategy_name='', order_remark=''):
        '''
        下单买
        :param stock_code: 证券代码, 例如"600000.SH"
        :param order_type: 委托类型, 23:买, 24:卖
        :param order_volume: 委托数量, 股票以'股'为单位, 债券以'张'为单位
        :param price_type: 报价类型, 详见帮助手册
        :param price: 报价价格, 如果price_type为指定价, 那price为指定的价格, 否则填0
        :param strategy_name: 策略名称
        :param order_remark: 委托备注
        :return: 返回下单请求序号, 成功委托后的下单请求序号为大于0的正整数, 如果为-1表示委托失败
        '''
        stock_code = self.adjust_stock_code(stock_code=stock_code)
        # 使用指定价下单，接口返回订单编号，后续可以用于撤单操作以及查询委托状态
        order_id = self.xt_trader.order_stock_async(account=self.acc, stock_code=stock_code,
                                                    order_type=order_type,
                                                    order_volume=order_volume, price_type=price_type,
                                                    price=price, strategy_name=strategy_name,
                                                    order_remark=order_remark)
        logger.info(f"交易类型{order_type} 代码{stock_code} 价格{price} 数量{order_volume} 订单编号{order_id}")
        return order_id

    def sell(self, stock_code, order_type=xtconstant.STOCK_SELL,
             order_volume=100, price_type=xtconstant.FIX_PRICE, price=20, strategy_name='', order_remark=''):
        '''
        下单卖出，参数说明参考买入的
        '''
        stock_code = self.adjust_stock_code(stock_code)
        # 使用指定价下单，接口返回订单编号，后续可以用于撤单操作以及查询委托状态
        order_id = self.xt_trader.order_stock_async(account=self.acc, stock_code=stock_code,
                                                    order_type=order_type,
                                                    order_volume=order_volume, price_type=price_type,
                                                    price=price, strategy_name=strategy_name,
                                                    order_remark=order_remark)
        logger.info(f"交易类型{order_type} 代码{stock_code} 价格{price} 数量{order_volume} 订单编号{order_id}")
        return order_id

    def adjust_stock_code(self, stock_code):
        """
        调整代码格式为 code.market
        :param stock_code: 代码
        :return: code.market格式的代码
        """
        code_len = len(stock_code)
        sh_start_2 = ['11']
        sh_start_3 = ['600', '601', '603', '688', '510', '511',
                      '512', '513', '515', '113', '110', '118', '501']
        if code_len == 6:
            if stock_code[:3] in sh_start_3 or stock_code[:2] in sh_start_2:
                post_code = stock_code + '.SH'
            else:
                post_code = stock_code + '.SZ'
        elif code_len == 9:
            post_code = stock_code.upper()
        else:
            logger.error(f"{stock_code} :代码格式错误")
            return
        logger.debug(f"{stock_code}代码调整为：{post_code}")
        return post_code

    def apply_slippage_by_percent(self, price, percent, side):
        """
        按百分比设置滑点
        :param price: 预期成交价格
        :param percent: 滑点百分比（例如：0.01表示1%）
        :param side: 交易方向，'buy' 或 'sell'
        :return: 实际成交价格
        """
        if side not in ['buy', 'sell']:
            raise ValueError("交易方向必须是 'buy' 或 'sell'")
        slippage_amount = price * percent
        if side == 'buy':
            actual_price = price + slippage_amount
        else:
            actual_price = price - slippage_amount
        return actual_price

    def apply_slippage_by_price(self, price, slip_price, side):
        """
        按价格处理滑点
        :param price: 价格
        :param slip_price: 滑动值 0.1：一毛
        :param side: 交易方向，'buy' 或 'sell'
        :return: 实际成交价格
        """
        if side not in ['buy', 'sell']:
            raise ValueError("交易方向必须是 'buy' 或 'sell'")
        if side == 'buy':
            actual_price = price + slip_price
        else:
            actual_price = price - slip_price
        return actual_price

    @property
    def balance(self):
        return self.xt_trader.query_stock_asset(self.acc).cash
    
    def total_asset(self):
        return self.xt_trader.query_stock_asset(self.acc).total_asset
    
    def query_stock_asset(self):
        # 资产查询
        return self.xt_trader.query_stock_asset(self.acc)

    def query_stock_orders(self, cancelable_only=False):
        # 委托查询
        return self.xt_trader.query_stock_orders(self.acc, cancelable_only=cancelable_only)

    def query_stock_trades(self):
        # 成交查询 当日所有成交
        return self.xt_trader.query_stock_trades(self.acc)

    def query_stock_positions(self):
        # 查询资金账号对应的持仓
        return self.xt_trader.query_stock_positions(self.acc)

    def cancel_order(self, order_id):
        # 撤单
        return self.xt_trader.cancel_order_stock_sysid_async(self.acc, order_id)

if __name__ == '__main__':
    path = rf"D:\国金证券QMT交易端\userdata_mini"
    session_id = 123456
    account = "**********"
    qmt_trader = QmtTrader(path, session_id, account)

