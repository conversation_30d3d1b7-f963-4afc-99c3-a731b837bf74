"""
高性能股票数据传输工具包
基于ZMQ实现的发布订阅和RPC通信模块

主要功能：
1. 高性能ZMQ发布订阅 - 用于实时股票tick数据传输
2. 高性能ZMQ RPC - 用于数据查询和交易指令
3. 股票数据桥接 - 提供高级封装和便捷接口
4. 股票RPC服务 - 完整的股票数据和交易服务

特性：
- 极高的性能和低延迟
- 支持多种序列化方式 (JSON, Pickle, MessagePack)
- 内存使用优化
- 异步处理支持
- 完善的错误处理和统计
- 专为股票高频数据优化

使用示例：

# 发布订阅模式
from tools import StockDataPublisher, StockDataSubscriber

# 启动发布者
publisher = StockDataPublisher(port=5555)
publisher.start()

# 发布tick数据
tick_data = {
    "stock_code": "000001.SZ",
    "timestamp": 1640995200000,
    "last_price": 15.68,
    "volume": 1000,
    "amount": 15680.0
}
publisher.publish_tick(tick_data)

# 启动订阅者
subscriber = StockDataSubscriber(server_address="localhost", port=5555)
subscriber.start()

# 订阅股票数据
def on_tick(tick_data):
    print(f"收到tick: {tick_data.stock_code} - {tick_data.last_price}")

subscriber.subscribe_stock("000001.SZ", on_tick)

# RPC模式
from tools import StockDataRPCService, StockDataRPCClient

# 启动RPC服务
service = StockDataRPCService(port=5556, data_manager=data_manager)
service.start()

# RPC客户端调用
client = StockDataRPCClient(server_address="localhost", port=5556)
client.connect()

# 获取最新tick数据
result = client.get_latest_tick("000001.SZ")
print(result)

# 获取历史数据
hist_data = client.get_historical_data("000001.SZ", count=100)
print(hist_data)
"""

from .zmq_pubsub import ZMQPublisher, ZMQSubscriber
from .zmq_rpc import ZMQRPCServer, ZMQRPCClient
from .stock_data_bridge import (
    StockDataPublisher, 
    StockDataSubscriber, 
    StockTickData
)
from .stock_rpc_service import (
    StockDataRPCService, 
    StockDataRPCClient
)

__version__ = "1.0.0"
__author__ = "Stock Quant System"

__all__ = [
    # 基础ZMQ组件
    "ZMQPublisher",
    "ZMQSubscriber", 
    "ZMQRPCServer",
    "ZMQRPCClient",
    
    # 股票数据专用组件
    "StockDataPublisher",
    "StockDataSubscriber",
    "StockTickData",
    "StockDataRPCService",
    "StockDataRPCClient"
]

# 版本信息
VERSION_INFO = {
    "version": __version__,
    "features": [
        "高性能ZMQ发布订阅",
        "高性能ZMQ RPC",
        "股票数据专用封装",
        "多种序列化支持",
        "异步处理",
        "性能统计",
        "错误处理"
    ],
    "dependencies": [
        "zmq",
        "msgpack",
        "pandas",
        "numpy"
    ]
}

def get_version_info():
    """获取版本信息"""
    return VERSION_INFO

def check_dependencies():
    """检查依赖包"""
    missing_deps = []
    
    try:
        import zmq
    except ImportError:
        missing_deps.append("pyzmq")
    
    try:
        import msgpack
    except ImportError:
        missing_deps.append("msgpack")
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    if missing_deps:
        print(f"缺少依赖包: {', '.join(missing_deps)}")
        print("请运行: pip install pyzmq msgpack pandas numpy")
        return False
    
    print("所有依赖包已安装")
    return True

# 性能配置建议
PERFORMANCE_CONFIG = {
    "publisher": {
        "hwm": 50000,  # 高水位标记
        "serialization": "msgpack",  # 推荐序列化方式
        "batch_mode": True,  # 批处理模式
        "flush_interval": 0.1  # 刷新间隔(秒)
    },
    "subscriber": {
        "hwm": 50000,
        "serialization": "msgpack",
        "buffer_size": 10000
    },
    "rpc_server": {
        "worker_threads": 8,  # 工作线程数
        "serialization": "msgpack",
        "timeout": 30  # 超时时间(秒)
    },
    "rpc_client": {
        "serialization": "msgpack",
        "timeout": 30
    }
}

def get_performance_config():
    """获取性能配置建议"""
    return PERFORMANCE_CONFIG

def create_optimized_publisher(port: int = 5555, **kwargs):
    """创建优化配置的发布者"""
    config = PERFORMANCE_CONFIG["publisher"].copy()
    config.update(kwargs)
    return StockDataPublisher(port=port, **config)

def create_optimized_subscriber(server_address: str = "localhost", 
                               port: int = 5555, **kwargs):
    """创建优化配置的订阅者"""
    config = PERFORMANCE_CONFIG["subscriber"].copy()
    config.update(kwargs)
    return StockDataSubscriber(server_address=server_address, port=port, **config)

def create_optimized_rpc_service(port: int = 5556, data_manager=None, 
                                trader=None, **kwargs):
    """创建优化配置的RPC服务"""
    config = PERFORMANCE_CONFIG["rpc_server"].copy()
    config.update(kwargs)
    return StockDataRPCService(port=port, data_manager=data_manager, 
                              trader=trader, **kwargs)

def create_optimized_rpc_client(server_address: str = "localhost", 
                               port: int = 5556, **kwargs):
    """创建优化配置的RPC客户端"""
    config = PERFORMANCE_CONFIG["rpc_client"].copy()
    config.update(kwargs)
    return StockDataRPCClient(server_address=server_address, port=port, **kwargs)
