"""
高性能股票数据传输工具包
基于ZMQ实现的发布订阅和RPC通信模块

主要功能：
1. 独立的ZMQ发布订阅模块 - 用于实时数据传输
2. 独立的ZMQ RPC模块 - 用于请求响应通信
3. 股票数据专用封装 - 提供高级接口
4. 股票RPC服务 - 完整的业务服务

模块独立性：
- ZMQ发布订阅和RPC是完全独立的模块，可以单独使用
- 股票数据封装是在基础模块上的高级封装
- 每个模块都有独立的配置和生命周期管理

特性：
- 极高的性能和低延迟
- 支持多种序列化方式 (JSON, Pickle, MessagePack)
- 内存使用优化
- 异步处理支持
- 完善的错误处理和统计
- 支持股票列表订阅，返回批量数据字典

使用示例：

# 1. 独立使用ZMQ发布订阅
from tools.zmq_pubsub import ZMQPublisher, ZMQSubscriber

publisher = ZMQPublisher(port=5555)
publisher.start()

# 发布批量数据
batch_data = {
    "000001.SZ": {"last_price": 15.68, "volume": 1000},
    "000002.SZ": {"last_price": 25.34, "volume": 2000}
}
publisher.publish_tick_batch(batch_data)

subscriber = ZMQSubscriber(port=5555)
subscriber.start()

# 订阅股票列表，返回批量数据
def on_batch_data(data_dict):
    print(f"收到批量数据: {data_dict}")

subscriber.subscribe_stock_list(["000001.SZ", "000002.SZ"], on_batch_data)

# 2. 独立使用ZMQ RPC
from tools.zmq_rpc import ZMQRPCServer, ZMQRPCClient

def get_data(stock_code):
    return {"stock_code": stock_code, "price": 15.68}

server = ZMQRPCServer(port=5556)
server.register_method("get_data", get_data)
server.start()

client = ZMQRPCClient(port=5556)
client.connect()
result = client.call("get_data", {"stock_code": "000001.SZ"})

# 3. 使用股票数据专用封装
from tools import StockDataPublisher, StockDataSubscriber

publisher = StockDataPublisher(port=5555)
subscriber = StockDataSubscriber(port=5555)

# 订阅股票列表，返回StockTickData对象字典
def on_stock_list(tick_dict):
    for code, tick_data in tick_dict.items():
        print(f"{code}: {tick_data.last_price}")

subscriber.subscribe_stock_list(["000001.SZ", "000002.SZ"], on_stock_list)
"""

from .zmq_pubsub import ZMQPublisher, ZMQSubscriber
from .zmq_rpc import ZMQRPCServer, ZMQRPCClient
from .stock_data_bridge import (
    StockDataPublisher, 
    StockDataSubscriber, 
    StockTickData
)
from .stock_rpc_service import (
    StockDataRPCService,
    StockDataRPCClient
)

# FastAPI服务器 (可选导入，需要安装fastapi和uvicorn)
try:
    from .fastapi_server import app as fastapi_app, start_server
    from .fastapi_integration import FastAPIIntegration
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    fastapi_app = None
    start_server = None
    FastAPIIntegration = None

__version__ = "1.0.0"
__author__ = "Stock Quant System"

__all__ = [
    # 基础ZMQ组件
    "ZMQPublisher",
    "ZMQSubscriber",
    "ZMQRPCServer",
    "ZMQRPCClient",

    # 股票数据专用组件
    "StockDataPublisher",
    "StockDataSubscriber",
    "StockTickData",
    "StockDataRPCService",
    "StockDataRPCClient",

    # FastAPI组件 (可选)
    "fastapi_app",
    "start_server",
    "FastAPIIntegration",
    "FASTAPI_AVAILABLE"
]

# 版本信息
VERSION_INFO = {
    "version": __version__,
    "features": [
        "高性能ZMQ发布订阅",
        "高性能ZMQ RPC",
        "股票数据专用封装",
        "多种序列化支持",
        "异步处理",
        "性能统计",
        "错误处理"
    ],
    "dependencies": [
        "zmq",
        "msgpack",
        "pandas",
        "numpy"
    ],
    "optional_dependencies": [
        "fastapi",
        "uvicorn",
        "pydantic"
    ]
}

def get_version_info():
    """获取版本信息"""
    return VERSION_INFO

def check_dependencies():
    """检查依赖包"""
    missing_deps = []
    
    try:
        import zmq
    except ImportError:
        missing_deps.append("pyzmq")
    
    try:
        import msgpack
    except ImportError:
        missing_deps.append("msgpack")
    
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    if missing_deps:
        print(f"缺少依赖包: {', '.join(missing_deps)}")
        print("请运行: pip install pyzmq msgpack pandas numpy")
        return False
    
    print("所有依赖包已安装")
    return True

# 性能配置建议
PERFORMANCE_CONFIG = {
    "publisher": {
        "hwm": 50000,  # 高水位标记
        "serialization": "msgpack",  # 推荐序列化方式
        "batch_mode": True,  # 批处理模式
        "flush_interval": 0.1  # 刷新间隔(秒)
    },
    "subscriber": {
        "hwm": 50000,
        "serialization": "msgpack",
        "buffer_size": 10000
    },
    "rpc_server": {
        "worker_threads": 8,  # 工作线程数
        "serialization": "msgpack",
        "timeout": 30  # 超时时间(秒)
    },
    "rpc_client": {
        "serialization": "msgpack",
        "timeout": 30
    }
}

def get_performance_config():
    """获取性能配置建议"""
    return PERFORMANCE_CONFIG

def create_optimized_publisher(port: int = 5555, **kwargs):
    """创建优化配置的发布者"""
    config = PERFORMANCE_CONFIG["publisher"].copy()
    config.update(kwargs)
    return StockDataPublisher(port=port, **config)

def create_optimized_subscriber(server_address: str = "localhost", 
                               port: int = 5555, **kwargs):
    """创建优化配置的订阅者"""
    config = PERFORMANCE_CONFIG["subscriber"].copy()
    config.update(kwargs)
    return StockDataSubscriber(server_address=server_address, port=port, **config)

def create_optimized_rpc_service(port: int = 5556, data_manager=None, 
                                trader=None, **kwargs):
    """创建优化配置的RPC服务"""
    config = PERFORMANCE_CONFIG["rpc_server"].copy()
    config.update(kwargs)
    return StockDataRPCService(port=port, data_manager=data_manager, 
                              trader=trader, **kwargs)

def create_optimized_rpc_client(server_address: str = "localhost", 
                               port: int = 5556, **kwargs):
    """创建优化配置的RPC客户端"""
    config = PERFORMANCE_CONFIG["rpc_client"].copy()
    config.update(kwargs)
    return StockDataRPCClient(server_address=server_address, port=port, **kwargs)
