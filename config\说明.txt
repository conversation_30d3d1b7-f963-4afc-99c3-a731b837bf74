        参数量化交易模型内部不外传
   miniqmt策略条件 20250609修正版（模型设计运行开始时间为20230408）                       
一，本模型标的前池：前池A/前池B/前池c//前池D前池E，其中前池A/前池B/前池c为文件传输外部读取；前池D前池E由qmt按照条件要求计算产生，池不同，对应的模型模型触发不同 。
1，大智慧(同花顺或者通达信)前池A路径
直接加载大智慧导出前池A逻辑至D:\quant_thirdparty_data\大智慧数据\前池\2024-06-19\qianchi.xlsx 时间依次类推，大智慧前池每天开盘前085000之前读取一次或者收盘后203000读取一次作为次日操作备选池，如果有变化以后面第二次读取的为准，不带字段，只读取代码和名称就可以；本池主要替代前池D，是由少数L2采集计算产生。
2，大智慧(同花顺或者通达信)前池B路径
每个交易日92600-92930完成读取，文件格式为scv或者约定的其它格式，路径为桌面，，，如果没有文件或者没有变化就不参与模型计算； 092500保证数据，092530~092957开始读取，以最后读取数据汇总为完全top池，由于可能出现延时计算，不能缺少标的；
3，大智慧(同花顺或者通达信)前池c路径
每个交易日93030-93500循环不超过15秒左右读取，文件格式为scv或者约定的其它格式，路径为桌面，，，如果没有文件或者没有变化就不参与模型计算
4，TOP系统计算前池D
同时支持qmt top前池D操作池逻辑和大智慧top操作池逻辑，两个top池可以同时使用，D:\quant_thirdparty_data\大智慧数据\top\2024-06-19\topi.xlsx 互不干扰；没有对应前池，不能影响其它正常操作。
qmt前池，qmt每日18点收盘后按照指标AAA从大到小计算出市场前150位个股，超出150位则使用指标BBB过滤掉超出部分 。本池算法如下（假定计算时间为当日开盘之前，如果当日收盘计算就需要注意时间周期的表述）： 
1.剔除昨日下影线超过12%的股票,即选出下影线小于12%的股票；
2.收盘价大于昨日之前最近3日最高价（20230610）
3.1.02*（昨日最大涨幅max_high_ratio）>=(昨日涨停价)，并且昨日收盘价大于或者等于开盘价
4.剔除昨天成交量/前一天成交量超过X倍并且前日未封板的股票
5.剔除昨天一字板并且换手率超过 X％的股票
6.剔除前天，大前天都是收阴的，昨天不是跳空高开（昨天最低价高于前天最高价）的股票
7.剔除昨日最低价超过昨日跌停价的1%的股票
8.剔除 （前一天一字板且 (昨开/前天收盘价-1)>X% 且 (前天收盘价/昨最低价-1) > X%）
9.昨日一字板并且前一天涨停并且前一天是收盘价大于开盘价并且昨天成交量/前一天成交量超过4倍的股票，剔除；20230609增加
10.昨日成交笔数超过15万笔的，剔掉；
11.昨日连板大于或者等于7连板并且昨日open=涨停价的剔除；
  剔除首板是一字板的连板股票
12.按账户需求，分别剔除688，4，8，30开头股票；
13.剔除ST，退市股票；
14.剔除5日内新股
15.剔除昨日是连续一字板大于等于3的个股（注意与T字板的区别不同）
16.剔除前日跌幅大于-4%并且振幅超过8%的个股
19.财务过滤条件，新国九条，触发分红条件但不分红可能st，需要剔除;  
20.处在立案调查没有结案的，可以从文本文件读取过滤为黑名单
4B，TOP系统计算盘中模型前池E
取指数成交额，换手率，开幅，计算方法为XXXX前10（过滤成分股超过400支次，少于15支次的），然后对应个股分别为前3（个股剔除ST，北交所，股价大于50元）
5，关于少数部分中长线标的持仓池，直接持有或者人工操作，机器不参与其买卖
示范# 不需要策略卖出股票
custom.not_sell_by_strategy = ['600657.SH', '002057.SZ', '300004.SZ', '002522.SZ', '002366.SZ', '000090.SZ', '600666.SH', '000620.SZ', '600654.SH', '300010.SZ', '300427.SZ', '000547.SZ']
# 不需要策略执行撤单逻辑的股票
custom.not_cancel_by_strategy = ['600657.SH', '002057.SZ', '300004.SZ', '002522.SZ', '002366.SZ', '000090.SZ', '600666.SH', '000620.SZ', '600654.SH', '300010.SZ', '300427.SZ', '000547.SZ']
 
 
 
二，风险控制
  最大仓位8成，最小仓位0；单股最大占比40%，注意同步微信特别提醒。
1、  当前证券帐户310902083805三个交易日连续未盈利 （含当日盘中），新开仓起额量较正常值减半；
2、  帐户收益一周未跑嬴沪深300指数，新开仓起量正常值减半；并且开仓标的减半
3、  是系统风险岀现（大盘指数或沪深3 00指数新低且沪深A股下跌家数昨日前日两天超过4000家同时今日开盘下跌家数超过3000家），或者（沪深2市总交易额连续3日下降，并且昨日首次低于1万亿），当日开仓模型不超过2种（待指定名称），开仓标的股不超过2支，每股操作买入金额不超过总额的20分之一或者持续禁仓至同时开盘后持续判定，如果上涨家数超过三千家以上并且成交额相对于昨天全天成交额超过1.15倍风险解除。
按照概率统计，除系统风险外，操作依据我们一般将市场划为：A可以不交易期 （昨日涨停今日跌停>5家并且今日跌停家数超过昨日跌停总家数，仓位控制<20%），B正常期操作期(市场连板高度突破3板并且进阶率大于15%，不是一字连板的和非ST)，C加速高潮期（封板率>85%并且昨日进阶率>40%，策略上必须执行尾盘无封板就全部清仓原则，包括中长线股票检查执行）。在交易日每3分钟或者5分钟自动更新一次市场情绪监控（大盘指数，自定义的涨停指数，封板率指数）。市场情绪对仓位控制，回避风险有帮助。
     当连续3笔交易或者持仓股60%触发止损时，自动启动策略休眠机制。
4、 总设计买入模型不超过N种，每种买入模型条件最多开仓数不超过3支 当天最多买入成交股票数不超过X只，可通过custom.max_buy_stocks_count_per_day控制。不包括挂单未成交数。
5、  其它特有风控细节，A:统计昨天涨停股票竞价板幅>N的数量是否占比超过M%，如果没有超过就只执行对应特定 buy_strategy_XX逻辑判定，如果超过就执行所有买入逻辑判定。
6, 北交所个股暂不执行机器买入，只执行手动买入，北交所买入个股不超过2只，最大仓位不超过1成；但是机器执行卖出策略。	
7，个股盘中N分钟涨速超过X%，拒接手工买入	；指定时刻xxyyzz比如133000后拒接手动买入；持仓个股股价连续2天下跌，拒接手工增仓买入，中长线豁免标的股除外；个股仓位占比超过20%以上同时该股盘中下跌超过N%禁止手工增仓买入。
8，盘中昨日涨停板家数下跌比超过50%，拒接手动买入非保护标的池个股
9，如果单笔资金超过1百万以上，除非涨停板买入策略外其它都使用超单策略，每笔最大买入不超过当时的委托卖一和买一总量的30%或者最大单笔买入限额1500万，不冲击市场。	
10，对每个模型触发标的股做对应的指数监控（包括买一买入成功但是预警触发的的个股），收盘后自动统计最近2天交易模型，取盈亏比最大的2个模型，自动作为次日交易触发优先策略（此条模型来调优，可以使用包含xgb、lightGBM、随机森林等更多方法，本次实现如果有难度，暂时不做）
  分析市场赚钱指数最强的前N，计算预警触发模型之间的差比。后面做策略的自动化优化。
11，系统黑名单禁止操作，采用文件读取方式，会不定期更新；有些数据暂时不能实现机器计算只能采用人工文件清单执行，主要是立案调查这种个股；没有就不过滤
12，重大事件驱动等重点标的交集优先，采用特指文件读取方式，每天机器8点-9点之前读取一次，重大事件池对应的策略为“重大事件策略买入模型”，作为前池A或者B，c,D等，如果有交集优先项优先买入，没有就不执行。
13,盘中总资产回撤超过N%，除非已经涨停板的个股，其它低于均价的个股都直接一键清仓
14，自动判定账户是否是融资融券，如果是，开仓优先使用融资功能，单一个股融资最大不超过3成仓；如果融资买入后，不再使用现金进行增仓买入，即使盘中其它模型触发改标的股
 15，定时不必要的清空数据和log文件，但是需要长期保存大盘及横截面相对应的封装数据；

三，封装调用指标函数及定义
每个模型触发的个股只买入一次，每个模型循环计算确保个股不重复买入；如果多个模型触发买入同一个股，则该股最大买入次数不超过4次，超过后后面的触发模型不再买入同一标的；并且同一只个股最大买入仓位累加不能超过40%
封装函数及具体定义 ，每一个指标都要求对应出指定的时刻，除非该指标的时刻是唯一的，比如开盘价，，，这样在后面的买卖模型里面自动生成与之相对应的数值；当然系统能够直接调用的指标除外。
涨量、跌量，涨额，跌额：一段时间里面当前tick成交价大于前一个tick的买一价就是涨量或者阳量，小于就是跌量或者阴量，相等是平量不参与张跌量计算，所有涨的tick的成交量相加就是这段时间的涨量，所有跌的tick的成交量相加就是这段时间的跌量；
财务指标1:流通盘，，，，
创业板：30开头
科创板：688科创板
北交所：4、9
主板：其他
    下面所有指标除非买一明确或者根本不需要时间外，某一个涉及到tick的都有时分秒对应的调用，
指标1：（委托买一量+委托买二量+委托买3量）*委托买一价/10000；（单位万元），比如93500时刻的那一笔tick，此时如果没有推送这一笔，就以接下来的最近的一笔作为计算标准，下面所有相同。
指标1B：（委托买一量+委托买二量+委托买3量）*（(委托买一价+委托买2价+委托买价）)/3/10000；（单位万元）
指标2：（委托买一量+委托买二量+委托买3量+委托买4量+委托买5量）*委托买一价/10000；
指标3：（委托买一量+委托买二量+委托买3量）/（委托买一量+委托买二量）；
指标4：（委托买一量+委托买二量+委托买3量+委托买4量+委托买5量）/(托买一量+委托买二量）；
指标5：（委托买一量+委托买二量+委托买3量）/流通盘*100；
指标6：（委托买一量+委托买二量）/委托卖一量；
指标6B，（委托买一量+委托买二量）/委托买一量；
指标7:（委托买一量+委托买二量+委托买3量+委托买4量+委托买5量）/(委托卖一量+委托卖二量+委托卖3量+委托卖4量+委托卖5量）；
指标8:sum(委托买一量+委托买二量+委托买3量+委托买4量++委托买5量）)/sum((委托卖一量+委托卖二量+委托卖3量+委托卖4量+委托卖5量））；
指标9:tick对应的成交量/流通盘*100
指标10:tick对应的累计总成交量/流通盘*100
指标11:tick对应的成交额
指标12:tick对应的累计总成交额
指标13:tick对应的委托买一量/流通盘*100
指标13B:tick及此前对应的最大买一量/流通盘*100，已经发生过的最大委托买量换手率
指标14:tick对应的委托卖一量/流通盘*100
指标14B:tick及此前对应的最大卖一量/流通盘*100，已经发生过的最大委托卖量换手率
指标15:tick对应的托买一量+委托买二量+委托买3量+委托买4量+委托买5量）；
指标15B:tick对应的（委托买一量+委托买二量+委托买3量+委托买4量+委托买5量）/流通盘*100；
指标16A:区间sum(成交价大于前一笔委托卖一价的量）
指标16C:区间sum(成交价大于前一笔委托卖N价的量）
指标16B:区间sum(成交价小于前一笔委托买一价的量）
指标16D:区间sum(成交价小于前一笔委托买N价的量）
指标17:区间sum(成交价大于前一笔成交价的金额）（单位万）
指标18:区间sum(成交价小于前一笔成交价的金额）
指标19:区间sum(成交价大于前一笔成交价的量）
指标19A:区间sum(成交价等于前一笔成交价的量）
指标20:区间sum(单笔成交价大于前一笔成交价的量/流通盘*100>N%）
指标20B:区间sum(单笔成交价小于前一笔成交价的量/流通盘*100>N%）
指标21:区间sum(单笔成交价大于前一笔成交价的金额>N）（单位万
指标21b:区间sum(单笔成交价小于前一笔成交价的金额<N）
指标22:区间max(单笔成交价小于前一笔成交价的量/流通盘*100）
指标22b:区间max(单笔成交价大于前一笔成交价的金额）（单位万）
指标23:区间sum(成交量<N的成交笔数）
指标24:区间sum(成交价大于前一笔成交价的成交笔数）
指标25:区间sum(成交价小于前一笔成交价的成交笔数）
指标26:tick对应的成交笔数
指标27:昨日总成交笔数
指标28:昨日总成交量
指标29：前池a指定时刻买一价=涨停价总数
指标30:昨日最大一字连板天数个股  开盘及涨停 无炸板(第一天也是) 
指标30A，昨日一字板   个股代码和数量
指标30B，前一日一字板 个股代码和数量
指标30C，昨日一字板前日最高价小于前日涨停价  个股代码和数量
指标30D，一字起板：第一个涨停板为一字板的个股。 个股代码和数量
指标31:tick对应的委托买一量/流通盘*100	
指标32:min(连续N笔tick对应的委托买一量/流通盘*100	)
指标33:max(连续N笔tick对应的委托买一量/流通盘*100	)
指标34:((min(连续N笔tick对应的委托买一价)/开盘价-1)*100
指标35:(max(连续N笔tick对应的委托买一价	)/开盘价-1)*100
指标36:（当前tick的买一价/竞价以来的最低价-1）*100；
指标37:（当前tick的买一价/竞价以来的最高价-1）*100；
指标37B:（指定时刻比如093500的tick的买一价/竞价以来的最高价-1）*100；
指标37C:（指定时刻比如093500及此前就是93000-93500tick的最大买一价/开盘价-1）*100，不一定就是093500那一刻发发生
指标38:（开盘价/竞价以来的最低价-1）*100；	
指标39:开盘以来的平均成交均价
指标40:开盘以来所有阳量的平均成交均价	（成交价大于前一笔的成交价为阳）
指标41:开盘以来所有阴量的平均成交均价（成交价小于前一笔的成交价为阳）
指标42:首次买一价=涨停价的时刻
指标43：首次卖一价=涨停价的时刻
指标44:首次炸板后再次买一价=涨停价的时刻
指标45：首次炸板后再次卖一价=涨停价的时刻
指标45+，二次封板时刻的买一量，连续N笔的买一量
指标45+，二次封板时刻的成交量，连续N笔的成交量	
指标46:首次涨停之前的最低价
指标47，首次开板后出现的最低价
指标47B，首次开板后出现的最低价/封板前的最低价的百分比；
指标48A，涨停板总成交量:计算包括当前tick在内的所有涨停价成交的量，不计算非涨停价的成交量
指标48D，涨停板成交量/流通盘*100
指标48E，涨停封单率：涨停（买一价=涨停价）时刻委买一量/流通盘（委买一量换手率） 首封
指标48B，统计买一价=涨停价所有的买一量的和
指标48C，撤单率：当前的委买1量减上1笔的委买1量减当前时刻的成交量／流通盘
指标49，统计涨停板开始第一笔至当前涨停价所有委托买一量/成交量  第一次涨停无炸板
指标50，统计连续n笔成交量/当前tick的委托买一量
指标51:统计连续N笔成交量/流通盘*100
指标52，首次开板以后到再次封板区间的阳量-阴量
指标53，指定时刻的成交量/前一笔的委托买一量
指标54，指定时刻的成交量/该tick后的N笔的最大成交量
指标55，指定时刻的成交价/该tick后的N笔的最大成交价
指标56，指定时刻的成交笔数/该tick后的N笔的最大成交笔数
指标57，涨停价（涨停价:10,20,30的不同）,跌停价，成本价，昨日收盘价，
    昨日最高价，昨日开盘价，昨日最低价， 昨日涨停价，（今日收盘价=涨停价，昨日收盘价=涨停价 暂时不要）字典都返回
指标57A，指定时刻的成交价，指定时刻的委托买一价
指标57B，开盘价：092506价格（防止延时，取92500后面的一笔成交价）
开盘第N笔价：（第0笔价:925006价；930000价：第一笔价，后面以此类推，以实际tick 发生顺序为准，因为可能093003没有数据

指标58，计算指定价成交的最大量
指标58B，计算成交的最大量对应的指定价和时分秒
指标59，计算指定价最大的委托买一量
指标60，计算指定价对应的最大委托卖一量
指标61，计算指定价所有对应的最大委托买2，3，4，5量 返回四个值
指标62，计算指定时刻标的池占比各自对应的证监会行业标的比，优先占比最大的前N支和涨幅top前N的交集标的
    证监会板块，20个 同一行业的数量 top的交集 第一个板块的n个
指标62B，计算指定时刻系统调用的板块概念指数涨跌幅，按照指数涨幅从大到小取前6位（
    如果不满足指数涨幅小于0.7%就不考虑），然后每个指数里面取涨幅前3（如果最低涨幅大于N%的全部进入），
    其中过滤北交所个股，过滤竞价一字板个股，剔除最低涨幅小于0.1%的个股，进入跟风观察池，
    分别计算出这些满足条件个股的概念交集数量已经该板块昨日涨停板占比，如果市场昨日最高标是属于该板块，
    则计算出所有最高标个股的概念数，再列表出条件池里面与最高标概念由交集的数量。
    竞价 看盘两笔 t概念  根据指数不再计算概念
指标63a，个股首次涨幅大于N条件判定，取top前X作为观察池预准备下单，下单条件XXXX 个股信息 个股数
指标63b，个股首次离涨停板小于N，条件判定，取topq前X准备下单，下单条件XXXX
指标64，分别统计昨日连板家数，昨日涨停总数，昨日2连板家数，昨日3连板家数，昨日4连板家数，昨日5连板家数，，昨日大于6连板总家数，
    指定时刻上涨N%的比例，下跌M%的比例，以及当前tick涨停家数和比例
指标65，昨日最高涨停板，最高连板（最高标）：昨日市场涨停连板数量最大的个股，如果数量相同的都成立，不包括ST 和首板一字板 起板的连板
 
指标65A，最高连板个股，竞价涨幅，指定时刻涨幅，成交量比昨日百分比，换手率，计算与最高连板所属证监会行业相同的昨日涨停板个股数及市场表现
指标65B，可能最高连板（非昨日最高标），比昨日最高连板低1-2个挡位，假定如果当然攻板或者封板就是最高连板的情况。这里需要耐心细致走分类 
指标66，开板次数
指标67，指定时刻涨幅，跌幅：（收盘价-昨日收盘价）/昨日收盘价*100	；
    振幅：（最高价-最低价）/昨收盘价*100 分开一下
指标68，指定时刻进行个股所属证监会行业计算，如果标的超过N家，
    过滤掉当前时刻暗盘比最小的一支以及暗盘率最小的一支，
    除非该股属于被保护对象或者瞄定优先标的
    暗盘自己计算

指标69,最后一笔买一价=涨停价的时刻，下一笔买一价小于涨停价;如果区间没有发生涨停，则标识0  返回发送的时候（炸板）
指标70:区间sum(单笔成交价大于或者等于前一笔成交价的量/流通盘*100）>N%
指标71:区间sum(单笔成交价大于或者等于前一笔成交价的金额）>N（单位万）
指标72:区间sum(单笔成交价小于前一笔成交价的金额》N）
指标73，（第一次tick的成交价/开盘价-1）*100>N 的时刻
指标74，标的池在指定指标XXx在指定时刻的TOP排序，按照从大到小排列，依次1.2，3.，，，
指标75，计算第一次买一价=指定价时刻前的最低价，使用（开盘价/最低价-1）*100；
指标76，计算第一次买一价=指定价时刻后第N笔买一量/第X笔的买一量
指标77，计算第一次买一价=指定价时刻后连续N笔成交量/第X笔的买一量
指标78，昨日涨停并且昨日收盘价大于开盘价
指标79，第一次出现买一价=指定价，计算出该时刻的委托买一量*委托买一价/10000；
指标80，第一次出现买一价=指定价，计算出该时刻的委托买一量/流通盘*100；
指标81，指定时刻的涨速，股价比开盘以来的最低价包括开盘价上涨速度，百分比，并且当时股价大于开盘价 
    价格 - 最低价/最低价
指标82，开盘至当前时刻（所有5档委托买入量-所有五档委托卖出量）/所有五档委托卖出量
指标83，((当前成交累计的总量和)-(所有tick的委托卖出5档量的累加和)/(所有tick的委托卖出5档量的累加和)
指标84，昨日成交量最大的单笔tick对应的成交价
指标85，卖一价=跌停价的卖一量 
指标85A，卖一价=跌停价的委托卖一量/流通盘*100
指标85B，卖一价=跌停价的单笔成交量
指标85C，卖一价=跌停价的连续N笔成交量	
指标85D，统计卖一价=跌停价的最大卖一量
指标85F，统计卖一价=跌停价，累加成交量	
指标85G，统计卖一价=跌停价，所有的委托买一量和
指标86，大盘指数
指标87，昨日连板指数，统计昨日2连板及以上个股平均，汇总制作封装今日竞价涨幅，
    盘中涨幅，盘中涨停家数比，成交额，换手率 
        做指数的方法 查阅下  指数的基本行情  87 88 89 都差不多
指标87B，昨日跌停板指数，个股今日竞价涨幅，盘中涨幅，盘中涨停家数比，，，
指标88，个股对应的概念板块昨日涨幅最大的板块指数
指标89，昨日首板指数，统计昨日首板及以上个股平均，汇总制作封装今日竞价涨幅，盘中涨幅，盘中涨停家数比，成交额，换手率
指标90，开盘涨幅，open/昨日收盘价的百分比
指标91，板幅：涨幅的幅度，即涨幅相对于涨停的比例（主板：涨幅/0.1，创业板：涨幅/0.2，北交所：涨幅/0.3）

指标92，自定义通用指定买入价检查:如果当前tick的买一量超过卖一量，
    同时统计N笔所有委买X档的总量比所有委卖X档量，前者/后者>N，
    并且统计最近N笔成交均价，如果当前价大于均价就以卖N价成交一笔，
    否则以最近N笔tick中买一-买五中最大的委托买入量对应的买价发生后成交一笔，
    不用挂买而是等待实际发生时刻买入；如果当前时刻买一价或者卖的一价等于涨停价，
    就以涨停价买入成交
指标93，针对账户可用资金判定，如果超过N万，除非指定价=涨停价买入的，其余使用买一价-0.01元等待该价出现再立即成交；如果成交量小于标准买入量则继续使用上一次买入价-0.01买入，一直到完全成交，但是要求不在盘口种出现挂买数据痕迹。也不冲击市场
指标94，N分钟内最低价低于昨日成本价
指标95，涨停后涨停价与最新价或者最低价回踩百分比， 返回两个
指标96，当日最高价与实时价差百分比
指标97，系统风险，大盘指数或沪深3 00指数低于上一次指数低点XXXX，
    或者连续一周放量大跌，且沪深A股下跌家数昨日前日两天超过4000家同时今日开盘下跌家数超过3000家
    返回 1或者0 200日的低点 返回两个大盘和 300 风险指数
指标98，普通行情，大盘指数或沪深3 00指数在10日线上方
指标99牛市行情定义:大盘指数或沪深3 00指数连续在3日线上方，并且连续3天收盘价大于开盘价，
    其中单日涨幅至少出现一次大于N%，成交量比比是一个交易日大于N，
指标100，L2数据的调用，一般指涨跌幅斜率主力净额斜率主力净量斜率
    https://vaserviece.10jqka.com.cn/Level2/index.php?&op=mainMonitorDetail&stockcode=300995
指标101，文件A文件B，，读取并且与前池进行交集计算，相同的作为优先

指标102，满足指标XXX在指定时刻按照从大到小top排序，取前N；
指标103，max(指标AAA，指标BBB)
指标104，如果标的属于证监会同一行业数量>N,按照指标aaA和指标bbB 排除到只有1家为止指标105，开盘后如果前池标的属于昨日涨停板家数最多的同一概念板块并且数量>N,按照指标aaA和指标bbB 排除到只有1家为止
指标105，某个指标AAA首次发生的时刻
指标106，某个指标AAA第二次发生的时刻
指标107，某个指标AAA第3二次发生的时刻  
指标108，等待买入，买入模型XX触发后，如果指标AAA<0,则等待N个tick行情发生，出现如果没有出现新的标的触发并且指标AAA>0,再立即买入；如果后者发生买入后者；如果后者出现X支，则选择AAA值最大的Y支标的立即买入。其实这也是逻辑判定  
指标109，立即买入，除非买入价=涨停价外，其它情况用 对方最优价买入
指标110，指定价买入，一般选项包括涨停价，委托卖2-5价，以及当前tick的2%价格笼子上限，请注意配置
指标111，读取个股每日成交回报就是龙虎榜数据，
    查询当日收盘4点半后的数据，对指定的少数营业部第一次净买入个股，
    比如“沈阳北大街营业部“等进行标志，次日如果出现触发交集信号，可以优先买入；
    同理，将机构买入占比纳入选股权重因子。
    交易所龙虎榜数据
指标112，对当日大宗交易进行盘后统计，溢价率较大予以监控；交易所公告
指标113，盘后或者盘中对涨停板集中度前6的概念板块予以统计，并且予以交集交叉分析标识。
    
四，大智慧前池A，B，C，及qmt池通用逻辑规则买入策略
  全系列买入策略时间一般有隔夜单策略，竞价策略，开盘策略，盘中策略和尾盘策略，以及特别同步异动策略。买入特征可以分为：隔夜挂买，竞价挂买，回踩板块T板回踩（低吸）（破板算法比较重要，），排板，回封包括T封，半路追，扫版，横截面跟风联动。涨停价扫板，差几档扫板，卖1手数 <= X手扫板，最高标策略等。排板，封单不足可自动撤单。标的涨停后监控，炸板算法确认后打回封板。多个条件说明":"多个买卖条件用英文的逗号隔开",并且在软件上给与对应可视化的中文买卖策略名称

A、  区间时刻1：这期间每个tick都判定， 
1.  buy_strategy_1："跟风模型"，
本模型需要应用的指标条件及参数要求，
先执行指标62B，然后
指定时刻A的指标1,指标2指标3
指定时刻B的指标1,指标2，指标3	，指标4，指标5
指定时刻C的指标1,指标2，指标3	，指标4，指标5，指标6
启用优先标的排序买入，除非没有 （以后方便添加）
触发条件：and 或者or  或者if，then，发生过，必须当前tick正在发生
指定时刻A的指标1>aa
(指定时刻A的指标1-指定时刻B的指标1)>bb,
(指定时刻B的指标1)>cc,
指定时刻B的指标2>dd
指定时刻B的指标3>ee
(指定时刻C的指标1)>ff
指定时刻C的指标2>gg
指定时刻C的指标3>hh
(指定时刻C的指标1)/指定时刻B的指标1)>ll,
指定时刻B的指标5>mm
指定时刻C的指标5>nn
指定时刻C的指标4/指定时刻B的指标4>qq	
指定时刻C的指标29/指标64>pp
如果指定时刻B的指标68发生，进行优选过滤
如果触发标的超过N家，则使用指标xx倒序直接过滤掉N+2,然后使用指标YY顺序发过滤掉2家，直到符合条件N；如果触发标的小于N直接买一价挂买
 同时过滤掉指标30D
上述条件成立，如果指标出现AA<N,则指标BB>X,

下单价格:委托买一价
下单金额:正常值或者风控值或者获利部分杠杠值
执行时刻与方式:立即买入，挂单买入还是等待买入的判定
下单标的数:不超过N只，或者占比总池不超过X %，相差不大
资金占比:不超过可使用现金的N%；如果本模型指定时刻触发标的少于N股，计划资金可以调出其它模型使用 
，如果没有出现优选算法，买点下单不执行前置买点和风险买点条件，仅执行标准买入条件。 
B、  区间时刻2：这期间每个tick都判定	，对应池为前池A，B，D池，如果没有默认为D池，如果有A池，则不执行D池买入计算
buy_strategy_2，" 开盘模型 "B
	指定时刻N <指标9<N1      指定时刻M <指标11<M1,
x<指标67<x1
1<指标69<N ，，，
下单价格:委托买一价或者条件另外有要求的
下单金额:正常值或者风控值或者获利部分杠杠值
下单标的数:不超过N只，或者占比总池不超过X %
资金占比:不超过可使用现金的N%;
比照原模型条件，请自动对应池前面的主板系列，实现参数化设置
 （93000-93800，时间周期可以调整，选择是否为主板还是创业板，或者全选，每个 tick 都判定，
handler_buy_strategy_11：handler_buy_strategy_1每个tick之前所有数据(包括92500时刻数据)计算涨速，如果涨速大于X%，并满足下面所有条件
a)统计筛选93000开盘后所有tick比前一笔上涨(最新价上涨)的tick(阳量)的成交额大于X万的情况，然后累加前面满足条件 tick的成交额求和超过Y万，一笔成交超过Y万的也算满足。（不计算92500）N
b)同时期间最低价比开盘价的回踩小于N%，（开盘价/最低价-1）*100<N
c)同时涨幅小于8%，(最新价/昨日收盘价-1）*100<8；如果当前时刻涨幅超过这个值，等待回踩满足条件再直接买入
d)同时统计每个tick（所有5档委托买入量-所有五档委托卖出量）/所有五档委托卖出量>=X倍以上(包括92500)
e)同时如果是创业板个股昨日连板< 1，主板个股昨日连板<=1；
f)同时统计出所有tick的成交量（当前tick成交量），只要当前tick价大于前一笔tick价其对应的成交量计算为阳量，所有阳量相加；只要当前tick价小于前一笔tick价其对应的成交量计算为阴量，所有阴量相加，要求触发时刻(阳量大于阴量或者)阳量与阴量比>0.9。
g)同时满足((当前成交累计的总量和)-(所有tick的委托卖出5档量的累加和)/(所有tick的委托卖出5档量的累加和)>X，(包括92500) 
h)统计93000开盘后包括093000但是不包括092500的tick出现过下面3个条件之一均可：（A，tick的成交量大于50万股并且大于100万元或者B:单笔tick成交额X万或者C:单笔tick成交量换手率>N%）
同时要求前池此时刻已经有最少一支个股涨停或者触发标的股为池中第一支涨停个股。
上述全部条件满足后，立即以卖方3档买入成交一笔,同时挂 open 价*0.98 买入一笔
如果买入金额超过当时卖出委托额，先减半买入，等待股价回落至当前的买一价再重新买入一半，不做挂买; 


） 
D,持仓增仓买入条件
判定连板指数指标出现竞价上涨，盘中涨幅家数大于跌幅家数，并且最高连板上涨汇总涨停；持仓策略原则不追，低吸；如果是昨日最高标或者次高标，原则尾盘无封板才最后清仓，盘中触发卖出正常执行但是不能完全清仓，毕竟概率大部分会尾盘再次拉升攻板。
buy_strategy_7：
指标XX>N，，，，，指定价，增仓买入一笔
	 
五，前池B及top操作池特别买入策略，
1,区间时刻6：这期间每个tick都判定
1、  handler_buy_strategy_11：
指标73<N,
指标81>=x%，并满足下面所有条件
a)  统计筛选93000开盘后指标21>x万，指标22>y万。（不计算92500）
b)  指标38<X%，
c)  同时涨幅小于N%，(最新价/昨日收盘价-1）*100<N；如果当前时刻涨幅超过这个值，等待回踩满足条件再直接买入
e)  同时调取条件:指标AA>0 , 指标BB >0，指标NN>0.2，如果是创业板个股昨日连板<X，主板个股昨日连板<=Y；
f)  指标19>指标20。
h)  统计开盘第n笔起第n笔包括的tick出现过：指标9>=n%
上述全部条件满足包括发生过，立即以卖方3档买入成交一笔,同时如果买入成交后出现（ open 价*x ，昨日收盘价），就用二者最低价增仓买入一笔
如果买入金额超过当时卖出委托额，先减半买入，等待股价回落至当前的买一价再重新买入一半;
2,handler_buy_strategy_12：分别计算指标AA，指标bb,指标CC，按照从大到小前10取交集，
满足指定时刻价比第N笔价大于n,买入X手股	
3,  handler_buy_strategy_13：创业板科创板一字开盘的并且开盘换手率低于X% 同时指定时刻买一量换手率> X%并且<Y%，挂涨停板 买入N手；
如果此模型3个条件触发买入与前5个模型有相同标的，同时前面条件已经买入成交而不是挂买未成交状态，则买入价统一变更为原买入价*X并且等待实际发生后直接买入；如果卖一相同，则原计划不变
 
六，卖出策略
