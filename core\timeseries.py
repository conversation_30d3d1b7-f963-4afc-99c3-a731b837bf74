"""
高性能时间序列数据管理系统
专为大规模股票市场数据设计，支持6000+股票的高频数据处理

特性：
- 极高的内存效率和处理速度
- 快速数据插入和检索
- 多种数据访问模式
- 针对每3秒一条数据的高频场景优化
"""

import pandas as pd
import numpy as np
from typing import Dict, Optional, Union, Tuple, Any
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')
default_columns = [
            'timestamp', 'last_price', 'high', 'low', 'pre_close', 'volume', 'amount',
            'bid1_price', 'bid2_price', 'bid3_price', 'bid4_price', 'bid5_price',
            'ask1_price', 'ask2_price', 'ask3_price', 'ask4_price', 'ask5_price',
            'bid1_vol', 'bid2_vol', 'bid3_vol', 'bid4_vol', 'bid5_vol',
            'ask1_vol', 'ask2_vol', 'ask3_vol', 'ask4_vol', 'ask5_vol',
            'change', 'is_yang', 'is_yin'
        ]
# 数据类型优化 - 使用更小的数据类型节省内存 默认float32 不提供的都是默认
default_dtype_map = {
            'timestamp': 'int64',  # 时间戳使用int64
            'amount': 'float64',
            'is_yang': 'bool',
            'is_yin': 'bool',
            'bid1_price': 'float64',
            'ask1_price': 'float64',
            'volume': 'int64',
            'bid1_vol': 'int64',
            'bid2_vol': 'int32',
            'bid3_vol': 'int32',
            'bid4_vol': 'int32',
            'bid5_vol': 'int32',
            'ask1_vol': 'int64',
            'ask2_vol': 'int32',
            'ask3_vol': 'int32',
            'ask4_vol': 'int32',
            'ask5_vol': 'int32'

        }
# 第一列必须是时间戳 名称 timestamp
class TimeSeriesManager:
    """
    高性能时间序列数据管理器
    """
    def __init__(self, 
                 initial_capacity: int = 4800, 
                 all_columns: list[str] = None,
                 dtype_map: dict[str, str] = None):
        """
        初始化时间序列管理器
        Args:
            initial_capacity: 容量）
        """
        self.initial_capacity = initial_capacity
        self.stock_data: Dict[str, pd.DataFrame] = {}
        self.stock_row_counts: Dict[str, int] = defaultdict(int)  # 当前行数
        if all_columns:
            self.all_columns = all_columns
        else:
            self.all_columns = default_columns
        if dtype_map:
            self.dtype_map = dtype_map
        else:
            self.dtype_map = default_dtype_map
        # 列位置映射（用于iat的最快访问）
        self.col_pos = {col: i for i, col in enumerate(self.all_columns)}
    
    def add_stocks(self, stock_codes: list[str]):
        """批量添加股票代码"""
        for stock_code in stock_codes:
            if stock_code not in self.stock_data:
                self.stock_data[stock_code] = self._create_empty_dataframe(stock_code)


    def _create_empty_dataframe(self, stock_code: str) -> pd.DataFrame:
        """
        为指定股票创建预分配的空DataFrame
        Args:
            stock_code: 股票代码
            
        Returns:
            预分配的DataFrame
        """
        # 创建预分配的DataFrame，填充NaN
        data = {}
        for col in self.all_columns:
            dtype = self.dtype_map.get(col, 'float32')
            if dtype.startswith('int'):
                data[col] = np.full(self.initial_capacity, 0, dtype=dtype)
            else:
                data[col] = np.full(self.initial_capacity, np.nan, dtype=dtype)
        self.stock_row_counts[stock_code] = -1
        df = pd.DataFrame(data)
        return df
    
    def add_stock_data(self, stock_code: str, data_dict: Dict[str, Union[int, float]]):
        """
        Args:
            stock_code: 股票代码
            data_dict: 数据字典，键为列名，值为数值
        """
        current_row = self.stock_row_counts[stock_code]
        current_row += 1
        df = self.stock_data[stock_code]
        # 构建完整的行数据
        row_values = np.array([data_dict[col] for col in self.all_columns],
                             dtype=object)
        # 一次性赋值整行 
        df.iloc[current_row] = row_values
        self.stock_row_counts[stock_code] = current_row

    def batch_add_data_optimized(self, batch_data: Dict[str, list[dict]]):
        """
        优化的批量添加数据方法（比逐条添加快10-20倍）
        Args:
            batch_data: 批量数据，格式为 {stock_code: [{}]}
        """
        for stock_code, infos in batch_data.items():
            df = self.stock_data[stock_code]
            rows = [[info[col] for col in self.all_columns] for info in infos]
            current_index = self.stock_row_counts[stock_code]
            current_index += 1
            row_ocunt = len(rows)
            end_index = current_index + row_ocunt
            # 批量赋值（比逐行赋值快很多）
            df = self.stock_data[stock_code]
            # 使用numpy数组进行批量赋值
            batch_array = np.array(rows, dtype=object)
            df.iloc[current_index:end_index] = batch_array
            self.stock_row_counts[stock_code] = end_index
    

    def _valid_len(self, stock_code: str) -> int:
        """返回有效数据行数（内部使用，O(1)）"""
        if stock_code not in self.stock_data:
            return 0
        n = self.stock_row_counts[stock_code]
        # 当前实现 row_counts 指向“最后一行索引”，需+1转换为长度
        return n + 1

    def get_valid_data(self, stock_code: str) -> pd.DataFrame:
        """
        获取指定股票的有效数据（排除预分配的空行）
        """
        valid_rows = self._valid_len(stock_code)
        if valid_rows == 0:
            return pd.DataFrame()
        return self.stock_data[stock_code].iloc[:valid_rows]
    
    def get_rows_by_index(self, stock_code: str, start_idx: int, count: int) -> pd.DataFrame:
        """
        按索引获取指定行数的数据
        Args:
            stock_code: 股票代码
            start_idx: 起始索引
            count: 获取行数
        Returns:
            指定行的DataFrame视图
        """
        valid_rows = self._valid_len(stock_code)
        if valid_rows == 0 or start_idx >= valid_rows:
            return pd.DataFrame()
        if count < 0:
            end_idx = valid_rows
        else:
            end_idx = min(start_idx + count, valid_rows)
        # iloc 切片：左闭右开，返回视图
        return self.stock_data[stock_code].iloc[start_idx:end_idx]
    
    def get_row_range(self, stock_code: str, start_idx: int, end_idx: int) -> pd.DataFrame:
        """
        获取指定行范围的数据
        
        Args:
            stock_code: 股票代码
            start_idx: 起始索引（包含）
            end_idx: 结束索引（包含）
            
        Returns:
            指定范围的DataFrame视图
        """
        valid_rows = self._valid_len(stock_code)
        # 归一化范围
        if valid_rows == 0 or end_idx < start_idx or start_idx >= valid_rows:
            return pd.DataFrame()
        end_i = min(end_idx + 1, valid_rows)  # iloc 右开
        return self.stock_data[stock_code].iloc[start_idx:end_i]

    def _find_timestamp_index(self, stock_code: str, timestamp: Union[int, np.integer], find_next: bool = True) -> Optional[int]:
        """
        使用 numpy.searchsorted 在有效数据范围内定位时间戳的行索引（O(log n)）

        Args:
            stock_code: 股票代码
            timestamp: 目标时间戳（整数，已按时间顺序插入）
            find_next: 若不存在精确匹配，True返回"下一个"，False返回"上一个"

        Returns:
            行索引（int），若无可用记录返回 None
        """
        n = self._valid_len(stock_code)
        if n == 0:
            return None
        df = self.stock_data[stock_code]
        ts_arr = df['timestamp'].values[:n]
        idx = int(np.searchsorted(ts_arr, timestamp, side='left'))
        # 命中
        if idx < n and ts_arr[idx] == timestamp:
            return idx
        # 回退策略
        if find_next:
            return idx if idx < n else None
        else:
            return (idx - 1) if idx > 0 else None

    def get_data_by_timestamp(self, stock_code: str, timestamp: int,
                             find_next: bool = True) -> Optional[pd.Series]:
        """
        根据时间戳获取数据

        Args:
            stock_code: 股票代码
            timestamp: 目标时间戳
            find_next: 如果找不到精确匹配，是否返回下一个记录（默认True）

        Returns:
            对应时间戳的数据行（Series），找不到返回None
        """
        idx = self._find_timestamp_index(stock_code, timestamp, find_next)
        if idx is None:
            return None
        # iloc 单次行访问（Series，零拷贝视图）
        return self.stock_data[stock_code].iloc[idx]

    def get_data_by_timestamp_range(self, stock_code: str, start_timestamp: int,
                                   end_timestamp: int) -> pd.DataFrame:
        """
        根据时间戳范围获取数据

        Args:
            stock_code: 股票代码
            start_timestamp: 起始时间戳
            end_timestamp: 结束时间戳

        Returns:
            时间戳范围内的DataFrame
        """
        n = self._valid_len(stock_code)
        if n == 0:
            return pd.DataFrame()
        df = self.stock_data[stock_code]
        ts_arr = df['timestamp'].values[:n]
        # numpy.searchsorted 批量二分定位边界
        start_idx = int(np.searchsorted(ts_arr, start_timestamp, side='left'))
        end_idx = int(np.searchsorted(ts_arr, end_timestamp, side='right'))
        if start_idx >= n or start_idx >= end_idx:
            return pd.DataFrame()
        return df.iloc[start_idx:end_idx]

    def get_cell_value(self, stock_code: str, row_idx: int, column: str) -> Any:
        """
        获取指定行列的单元格值（高性能访问）
        Args:
            stock_code: 股票代码
            row_idx: 行索引
            column: 列名

        Returns:
            单元格值
        """
        if stock_code not in self.stock_data:
            return None
        n = self._valid_len(stock_code)
        if row_idx is None or row_idx >= n or n <= 0:
            return None
        df = self.stock_data[stock_code]
        # 使用 iat + 预先映射的列位置，标量访问最快
        col_pos = self.col_pos.get(column)
        if col_pos is None:
            return None
        return df.iat[row_idx, col_pos]

    def get_value_by_timestamp(self, stock_code: str, timestamp: int, column: str,
                              find_next: bool = True) -> Any:
        """
        根据时间戳和列名获取特定值
        Args:
            stock_code: 股票代码
            timestamp: 时间戳
            column: 列名
            find_next: 如果找不到精确匹配，是否返回下一个记录的值
        Returns:
            指定时间戳和列的值
        """
        idx = self._find_timestamp_index(stock_code, timestamp, find_next)
        if idx is None:
            return None
        df = self.stock_data[stock_code]
        col_pos = self.col_pos.get(column)
        if col_pos is None:
            return None
        return df.iat[idx, col_pos]

    # 统一股票管理功能
    def get_all_stock_codes(self) -> list:
        """
        获取所有股票代码列表
        Returns:
            股票代码列表
        """
        return list(self.stock_data.keys())

    def get_stock_count(self) -> int:
        """
        获取股票总数

        Returns:
            股票数量
        """
        return len(self.stock_data)

    def get_stock_data_count(self, stock_code: str) -> int:
        """
        获取指定股票的数据条数
        Args:
            stock_code: 股票代码

        Returns:
            数据条数
        """
        return self.stock_row_counts.get(stock_code, 0)

    def get_memory_usage(self) -> Dict[str, Any]:
        """
        获取内存使用情况

        Returns:
            内存使用统计信息
        """
        total_memory = 0
        stock_memory = {}

        for stock_code, df in self.stock_data.items():
            memory_bytes = df.memory_usage(deep=True).sum()
            stock_memory[stock_code] = {
                'memory_mb': memory_bytes / (1024 * 1024),
                'rows': self.stock_row_counts[stock_code],
                'allocated_rows': len(df)
            }
            total_memory += memory_bytes

        return {
            'total_memory_mb': total_memory / (1024 * 1024),
            'stock_count': len(self.stock_data),
            # 'stock_details': stock_memory
        }

    def get_latest_data(self, stock_code: str, count: int = 1) -> pd.DataFrame:
        """
        获取最新的N条数据
        Args:
            stock_code: 股票代码
            count: 获取的数据条数

        Returns:
            最新的数据
        """
        valid_rows = self.stock_row_counts[stock_code]
        if valid_rows == 0:
            return pd.DataFrame()
        start_idx = max(0, valid_rows - count)
        return self.stock_data[stock_code].iloc[start_idx:valid_rows]

    def get_last_row(self, stock_code: str) -> pd.Series:
        try:
            index = self.stock_row_counts[stock_code]
        except KeyError:
            return None
        return self.stock_data[stock_code].iloc[index]
    # 文件输出功能
    def export_stock_to_csv(self, stock_code: str, filepath: str, include_invalid: bool = False):
        """
        将指定股票数据导出到CSV文件

        Args:
            stock_code: 股票代码
            filepath: 输出文件路径
            include_invalid: 是否包含无效数据（预分配的空行）
        """
        if stock_code not in self.stock_data:
            raise ValueError(f"股票代码 {stock_code} 不存在")

        if include_invalid:
            df = self.stock_data[stock_code]
        else:
            df = self.get_valid_data(stock_code)

        df.to_csv(filepath, index=False, encoding='utf-8-sig')

    def export_all_stocks_to_csv(self, output_dir: str, include_invalid: bool = False):
        """
        将所有股票数据导出到CSV文件
        Args:
            output_dir: 输出目录
            include_invalid: 是否包含无效数据
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        for stock_code in self.stock_data.keys():
            filepath = os.path.join(output_dir, f"{stock_code}.csv")
            self.export_stock_to_csv(stock_code, filepath, include_invalid)

    def export_summary_to_file(self, filepath: str):
        """
        导出数据摘要信息到文件

        Args:
            filepath: 输出文件路径
        """
        summary = {
            'stock_count': self.get_stock_count(),
            'memory_usage': self.get_memory_usage(),
            'stock_data_counts': {code: self.get_stock_data_count(code)
                                for code in self.get_all_stock_codes()},
            'latest_timestamps': {code: self.get_latest_timestamp(code)
                                for code in self.get_all_stock_codes()}
        }
        import json
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)

    def __repr__(self) -> str:
        """
        返回对象的字符串表示
        """
        return (f"TimeSeriesManager("
                f"stocks={len(self.stock_data)}, "
                f"total_records={sum(self.stock_row_counts.values())}, "
                f"memory={self.get_memory_usage()['total_memory_mb']:.2f}MB)")




if __name__ == "__main__":
    import time    
    manger = TimeSeriesManager()
    manger.add_stocks(range(6000))
    print(manger)
    print(len(manger.stock_data[10]["timestamp"]))
    print(manger.get_stock_count())
    time.sleep(5)
    print(manger.get_memory_usage())

    time.sleep(15)
    print(manger.get_memory_usage())
    
    
