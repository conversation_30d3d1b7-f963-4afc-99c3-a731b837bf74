# 高性能股票数据传输工具包

基于ZMQ实现的高性能股票tick数据发布订阅和RPC通信模块，专为股票量化交易系统设计。

## 特性

- **极高性能**: 基于ZMQ的高性能消息传输，支持每秒数万条消息
- **低延迟**: 针对股票tick数据优化，延迟控制在毫秒级别
- **内存优化**: 高效的内存使用和数据缓存机制
- **多种序列化**: 支持JSON、Pickle、MessagePack等序列化方式
- **异步处理**: 支持异步消息处理和批量操作
- **完善监控**: 内置性能统计和错误处理机制
- **易于使用**: 提供高级封装和便捷接口
- **模块独立**: ZMQ发布订阅和RPC完全独立，可单独使用
- **股票列表订阅**: 支持订阅股票列表，返回 `{股票代码: 数据}` 格式

## 安装依赖

### 核心依赖
```bash
pip install pyzmq msgpack pandas numpy
```

### 完整依赖 (包含FastAPI)
```bash
pip install -r tools/requirements.txt
```

### 或者分别安装
```bash
# 核心ZMQ功能
pip install pyzmq msgpack pandas numpy

# FastAPI HTTP服务 (可选)
pip install fastapi uvicorn pydantic

# 测试工具 (可选)
pip install requests pytest
```

## 核心组件

### 1. 发布订阅模式 (Pub/Sub)

用于实时股票tick数据的高效分发：

```python
from tools import StockDataPublisher, StockDataSubscriber

# 发布者
publisher = StockDataPublisher(port=5555)
publisher.start()

# 发布tick数据
tick_data = {
    "stock_code": "000001.SZ",
    "timestamp": 1640995200000,
    "last_price": 15.68,
    "volume": 1000,
    "amount": 15680.0,
    "bid1_price": 15.67,
    "ask1_price": 15.69
}
publisher.publish_tick(tick_data)

# 订阅者
subscriber = StockDataSubscriber(server_address="localhost", port=5555)
subscriber.start()

def on_tick(tick_data):
    print(f"收到tick: {tick_data.stock_code} - {tick_data.last_price}")

subscriber.subscribe_stock("000001.SZ", on_tick)
```

### 2. 独立使用ZMQ模块

#### 独立ZMQ发布订阅

```python
from tools.zmq_pubsub import ZMQPublisher, ZMQSubscriber

# 发布者
publisher = ZMQPublisher(port=5555, serialization="msgpack")
publisher.start()

# 发布批量数据
batch_data = {
    "000001.SZ": {"last_price": 15.68, "volume": 1000},
    "000002.SZ": {"last_price": 25.34, "volume": 2000}
}
publisher.publish_tick_batch(batch_data)

# 订阅者
subscriber = ZMQSubscriber(port=5555, serialization="msgpack")
subscriber.start()

# 订阅股票列表，返回批量数据字典
def on_batch_data(data_dict):
    print(f"收到批量数据: {data_dict}")
    # data_dict = {"000001.SZ": {...}, "000002.SZ": {...}}

subscriber.subscribe_stock_list(["000001.SZ", "000002.SZ"], on_batch_data)
```

#### 独立ZMQ RPC

```python
from tools.zmq_rpc import ZMQRPCServer, ZMQRPCClient

# 服务端
def get_stock_data(stock_codes):
    return {"000001.SZ": {"price": 15.68}, "000002.SZ": {"price": 25.34}}

server = ZMQRPCServer(port=5556)
server.register_method("get_data", get_stock_data)
server.start()

# 客户端
client = ZMQRPCClient(port=5556)
client.connect()
result = client.call("get_data", {"stock_codes": ["000001.SZ", "000002.SZ"]})
client.disconnect()
```

### 3. FastAPI HTTP服务

提供RESTful API接口用于HTTP请求获取数据：

```python
from tools.fastapi_server import start_server

# 启动FastAPI服务器
start_server(host="0.0.0.0", port=8000, data_manager=your_data_manager)

# 访问API
# GET http://localhost:8000/api/v1/tick/latest - 获取所有最新tick数据
# GET http://localhost:8000/api/v1/tick/000001.SZ - 获取指定股票tick数据
# GET http://localhost:8000/api/v1/minute/000001.SZ?count=240 - 获取分钟数据
# GET http://localhost:8000/api/v1/daily/000001.SZ?count=30 - 获取日线数据
# GET http://localhost:8000/docs - API文档
```

### 4. RPC模式

用于数据查询和交易指令：

```python
from tools import StockDataRPCService, StockDataRPCClient

# RPC服务端
service = StockDataRPCService(port=5556, data_manager=your_data_manager)
service.start()

# RPC客户端
client = StockDataRPCClient(server_address="localhost", port=5556)
client.connect()

# 获取最新tick数据
result = client.get_latest_tick("000001.SZ")
print(result)

# 获取历史数据
hist_data = client.get_historical_data("000001.SZ", count=100)
print(hist_data)
```

## FastAPI HTTP服务详解

### 服务器启动

#### 独立启动
```python
from tools.fastapi_server import start_server

# 使用模拟数据启动
start_server(host="0.0.0.0", port=8000)

# 集成数据管理器启动
start_server(host="0.0.0.0", port=8000,
            data_manager_instance=your_data_manager,
            rpc_client_instance=your_rpc_client)
```

#### 集成启动
```python
from tools.fastapi_integration import FastAPIIntegration

# 创建集成管理器
api_integration = FastAPIIntegration(
    data_manager=your_data_manager,
    api_host="0.0.0.0",
    api_port=8000
)

# 启动API服务器
api_integration.start_api_server()
```

### API接口说明

#### 基础接口
- `GET /` - API信息
- `GET /health` - 健康检查
- `GET /docs` - Swagger API文档
- `GET /api/v1/stats` - 服务器统计

#### Tick数据接口
- `GET /api/v1/tick/latest` - 获取所有最新tick数据
- `GET /api/v1/tick/latest?stock_codes=000001.SZ,000002.SZ` - 获取指定股票列表
- `GET /api/v1/tick/{stock_code}` - 获取单个股票tick数据

#### 历史数据接口
- `GET /api/v1/minute/{stock_code}?count=240&period=1m` - 获取分钟数据
- `GET /api/v1/daily/{stock_code}?count=30` - 获取日线数据

#### 市场数据接口
- `GET /api/v1/market/snapshot` - 获取市场快照

### 响应格式

```json
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "timestamp": 1640995200000,
  "count": 10
}
```

### 使用示例

```python
import requests

# 获取指定股票列表数据 - 核心功能
response = requests.get(
    "http://localhost:8000/api/v1/tick/latest",
    params={"stock_codes": "000001.SZ,000002.SZ,600000.SH"}
)
data = response.json()

if data["success"]:
    # 返回格式: {"000001.SZ": {...}, "000002.SZ": {...}, "600000.SH": {...}}
    for stock_code, tick_data in data["data"].items():
        print(f"{stock_code}: 价格={tick_data['last_price']}, 成交量={tick_data['volume']}")
```

## 核心功能：股票列表订阅

### 功能说明

客户端可以订阅一个股票列表，服务端会返回对应的数据字典格式：

- **输入**: `["000001.SZ", "000002.SZ", "600000.SH"]` (股票代码列表)
- **输出**: `{"000001.SZ": {数据}, "000002.SZ": {数据}, "600000.SH": {数据}}` (数据字典)

### 两种实现方式

#### 方式1: 批量数据主题订阅
```python
# 服务端发布批量数据到 "tick.batch" 主题
# 客户端订阅并过滤出需要的股票数据
subscriber.subscribe_stock_list(["000001.SZ", "000002.SZ"], callback)
```

#### 方式2: 单个股票聚合订阅
```python
# 客户端订阅多个单独的股票主题，然后聚合数据
# 当收集到所有订阅股票的数据时，触发回调
subscriber.subscribe_stock_list_individual(["000001.SZ", "000002.SZ"], callback)
```

### 使用示例

```python
from tools import StockDataPublisher, StockDataSubscriber

# 发布者
publisher = StockDataPublisher(port=5555)
publisher.start()

# 发布市场数据
market_data = {
    "000001.SZ": {"last_price": 15.68, "volume": 1000},
    "000002.SZ": {"last_price": 25.34, "volume": 2000},
    "600000.SH": {"last_price": 8.92, "volume": 3000}
}
publisher.publish_market_snapshot(market_data)

# 订阅者
subscriber = StockDataSubscriber(port=5555)
subscriber.start()

def handle_my_stocks(tick_dict):
    """处理我关注的股票数据"""
    for stock_code, tick_data in tick_dict.items():
        print(f"{stock_code}: 价格={tick_data.last_price}, 成交量={tick_data.volume}")

# 只订阅我关注的股票，服务端会过滤并返回对应数据
my_stocks = ["000001.SZ", "000002.SZ"]
subscriber.subscribe_stock_list(my_stocks, handle_my_stocks)

# 输出:
# 000001.SZ: 价格=15.68, 成交量=1000
# 000002.SZ: 价格=25.34, 成交量=2000
# (不会收到600000.SH的数据)
```

## 详细使用指南

### 发布订阅模式详解

#### 发布者 (Publisher)

```python
from tools import StockDataPublisher

# 创建发布者，支持多种配置
publisher = StockDataPublisher(
    port=5555,                    # 发布端口
    serialization="msgpack"       # 序列化方式: json/pickle/msgpack
)

publisher.start()

# 单个tick发布
publisher.publish_tick(tick_data)

# 批量发布
tick_list = [tick1, tick2, tick3]
publisher.publish_tick_batch(tick_list)

# 市场快照发布
market_data = {"000001.SZ": tick1, "000002.SZ": tick2}
publisher.publish_market_snapshot(market_data)

# 获取性能统计
stats = publisher.get_stats()
print(f"发送速度: {stats['messages_per_second']} msg/s")

publisher.stop()
```

#### 订阅者 (Subscriber)

```python
from tools import StockDataSubscriber

# 创建订阅者
subscriber = StockDataSubscriber(
    server_address="localhost",
    port=5555,
    serialization="msgpack"
)

subscriber.start()

# 1. 订阅特定股票 (单个处理)
def handle_stock_tick(tick_data):
    print(f"{tick_data.stock_code}: {tick_data.last_price}")

subscriber.subscribe_stock("000001.SZ", handle_stock_tick)

# 2. 批量订阅 (单个处理)
stock_codes = ["000001.SZ", "000002.SZ", "600000.SH"]
subscriber.subscribe_stocks(stock_codes, handle_stock_tick)

# 3. 订阅股票列表 (批量数据字典) - 核心功能
def handle_stock_list(tick_dict):
    """处理股票列表数据 - 返回 {股票代码: StockTickData} 格式"""
    print(f"收到 {len(tick_dict)} 只股票数据:")
    for stock_code, tick_data in tick_dict.items():
        print(f"  {stock_code}: 价格={tick_data.last_price}, 成交量={tick_data.volume}")

# 订阅指定的股票列表，服务端返回对应的数据字典
subscriber.subscribe_stock_list(["000001.SZ", "000002.SZ", "600000.SH"], handle_stock_list)

# 4. 订阅股票列表 (通过单个股票聚合)
def handle_aggregated_list(tick_dict):
    """处理聚合的股票数据"""
    print(f"聚合收到 {len(tick_dict)} 只股票数据")

subscriber.subscribe_stock_list_individual(["000001.SZ", "000002.SZ"], handle_aggregated_list)

# 5. 订阅所有股票
subscriber.subscribe_all_stocks(handle_stock_tick)

# 6. 订阅市场快照
def handle_market_snapshot(market_data):
    print(f"市场快照: {len(market_data)} 只股票")

subscriber.subscribe_market_snapshot(handle_market_snapshot)

# 获取最新数据
latest_tick = subscriber.get_latest_tick("000001.SZ")
all_ticks = subscriber.get_latest_ticks()

subscriber.stop()
```

### RPC模式详解

#### RPC服务端

```python
from tools import StockDataRPCService

# 创建RPC服务，需要传入数据管理器和交易接口
service = StockDataRPCService(
    port=5556,
    data_manager=your_data_manager,  # 数据管理器实例
    trader=your_trader              # 交易接口实例
)

service.start()

# 服务会自动注册以下RPC方法：
# - get_latest_tick: 获取最新tick
# - get_latest_ticks: 获取多个最新tick
# - get_market_snapshot: 获取市场快照
# - get_historical_data: 获取历史数据
# - calculate_ma/macd/rsi/bollinger: 技术指标计算
# - place_order/cancel_order: 交易操作
# - get_positions/get_account_info: 账户查询
# - ping: 心跳检测

# 保持服务运行
try:
    while True:
        time.sleep(1)
except KeyboardInterrupt:
    service.stop()
```

#### RPC客户端

```python
from tools import StockDataRPCClient

client = StockDataRPCClient(server_address="localhost", port=5556)
client.connect()

# 数据查询
tick_data = client.get_latest_tick("000001.SZ")
all_ticks = client.get_latest_ticks(["000001.SZ", "000002.SZ"])
market_snapshot = client.get_market_snapshot()
hist_data = client.get_historical_data("000001.SZ", count=100)

# 交易操作
order_result = client.place_order(
    stock_code="000001.SZ",
    action="buy",
    quantity=1000,
    price=15.68,
    order_type="limit"
)

positions = client.get_positions()

# 心跳检测
ping_result = client.ping()

client.disconnect()
```

## 性能优化配置

### 高性能配置

```python
from tools import create_optimized_publisher, create_optimized_subscriber

# 使用优化配置创建组件
publisher = create_optimized_publisher(port=5555)
subscriber = create_optimized_subscriber(port=5555)

# 手动配置
publisher = StockDataPublisher(
    port=5555,
    serialization="msgpack",    # 最快的序列化方式
    hwm=50000,                 # 高水位标记
    batch_mode=True,           # 批处理模式
    flush_interval=0.1         # 100ms刷新间隔
)
```

### 性能监控

```python
# 获取性能统计
pub_stats = publisher.get_stats()
print(f"发送速度: {pub_stats['messages_per_second']} msg/s")
print(f"缓冲区大小: {pub_stats['buffer_size']}")

sub_stats = subscriber.get_stats()
print(f"接收速度: {sub_stats['messages_per_second']} msg/s")
print(f"订阅数量: {sub_stats['subscribed_stocks']}")
```

## 数据格式

### 标准Tick数据格式

```python
{
    "stock_code": "000001.SZ",      # 股票代码
    "timestamp": 1640995200000,     # 时间戳(毫秒)
    "last_price": 15.68,            # 最新价
    "volume": 1000,                 # 成交量
    "amount": 15680.0,              # 成交额
    "open": 15.50,                  # 开盘价
    "high": 15.80,                  # 最高价
    "low": 15.45,                   # 最低价
    "pre_close": 15.60,             # 昨收价
    
    # 五档行情
    "bid1_price": 15.67, "bid1_vol": 100,
    "bid2_price": 15.66, "bid2_vol": 200,
    "bid3_price": 15.65, "bid3_vol": 300,
    "bid4_price": 15.64, "bid4_vol": 400,
    "bid5_price": 15.63, "bid5_vol": 500,
    
    "ask1_price": 15.69, "ask1_vol": 100,
    "ask2_price": 15.70, "ask2_vol": 200,
    "ask3_price": 15.71, "ask3_vol": 300,
    "ask4_price": 15.72, "ask4_vol": 400,
    "ask5_price": 15.73, "ask5_vol": 500
}
```

## 示例代码

### 完整系统示例

查看 `examples.py` 文件获取完整的使用示例：

```bash
# 运行发布者示例
python tools/examples.py publisher

# 运行订阅者示例
python tools/examples.py subscriber

# 运行RPC服务示例
python tools/examples.py rpc_service

# 运行RPC客户端示例
python tools/examples.py rpc_client

# 运行完整系统示例
python tools/examples.py integrated

# 运行性能测试
python tools/examples.py performance
```

### 独立模块示例

查看 `standalone_examples.py` 文件获取独立使用示例：

```bash
# 独立ZMQ发布订阅示例
python tools/standalone_examples.py pubsub

# 独立ZMQ RPC示例
python tools/standalone_examples.py rpc

# 混合使用示例
python tools/standalone_examples.py mixed
```

### 股票列表订阅测试

查看 `test_stock_list_subscription.py` 文件测试股票列表订阅功能：

```bash
# 基础ZMQ股票列表订阅测试
python tools/test_stock_list_subscription.py basic

# 高级封装股票列表订阅测试
python tools/test_stock_list_subscription.py highlevel

# 单个股票聚合订阅测试
python tools/test_stock_list_subscription.py individual

# 运行所有测试
python tools/test_stock_list_subscription.py all
```

### FastAPI HTTP服务示例

启动FastAPI服务器：

```bash
# 独立启动FastAPI服务器 (使用模拟数据)
python tools/fastapi_integration.py standalone

# 使用模拟数据管理器启动
python tools/fastapi_integration.py mock

# 与ZMQ系统集成启动
python tools/fastapi_integration.py zmq
```

测试FastAPI接口：

```bash
# 运行所有API测试
python tools/test_fastapi_client.py all

# 测试特定接口
python tools/test_fastapi_client.py tick      # 测试tick数据接口
python tools/test_fastapi_client.py market   # 测试市场数据接口
python tools/test_fastapi_client.py performance # 性能测试
```

直接访问API：

```bash
# 使用curl测试
curl http://localhost:8000/health
curl http://localhost:8000/api/v1/tick/latest
curl "http://localhost:8000/api/v1/tick/latest?stock_codes=000001.SZ,000002.SZ"
curl http://localhost:8000/api/v1/tick/000001.SZ
curl http://localhost:8000/api/v1/minute/000001.SZ?count=60
curl http://localhost:8000/api/v1/daily/000001.SZ?count=10
curl http://localhost:8000/api/v1/market/snapshot
```

## 性能基准

在标准配置下的性能表现：

- **发布速度**: 50,000+ 消息/秒
- **订阅延迟**: < 1ms
- **内存使用**: 优化的缓存机制，支持6000+股票
- **CPU使用**: 多线程处理，高效利用多核
- **网络带宽**: MessagePack序列化，数据压缩率高

## 最佳实践

1. **序列化选择**: 推荐使用MessagePack，平衡了速度和大小
2. **批处理**: 启用批处理模式可显著提高吞吐量
3. **缓冲区**: 根据数据量调整高水位标记(HWM)
4. **网络**: 使用千兆网络以获得最佳性能
5. **监控**: 定期检查性能统计，及时发现瓶颈

## 故障排除

### 常见问题

1. **连接失败**: 检查端口是否被占用，防火墙设置
2. **数据丢失**: 调整HWM设置，检查网络带宽
3. **延迟过高**: 优化序列化方式，减少批处理间隔
4. **内存占用**: 调整缓存大小，定期清理过期数据

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
publisher = StockDataPublisher(port=5555)
publisher.start()
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
