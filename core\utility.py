"""
General utility functions.
"""

import json
import csv
import datetime
# from datetime import datetime, time
from pathlib import Path
from collections.abc import Callable
from decimal import Decimal
from math import floor, ceil

import numpy as np


def read_csv(file_path: str, encoding: str = "utf-8-sig") -> list:
    """
    Read csv file. 读出字典格式
    """
    with open(file_path, "r", encoding=encoding) as f:
        reader = csv.DictReader(f)
        data = [row for row in reader]
    return data

def write_csv(file_path: str, data: list[dict], encoding: str = "utf-8") -> None:
     with open(file_path, 'w', newline='', encoding=encoding) as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(data[0].keys())
        for row in data:
            writer.writerow(row.values())
 

def time_string_to_hms_int(time_str):
    dt = datetime.datetime.strptime(time_str, "%H:%M:%S")
    hour = dt.hour
    minute = dt.minute
    second = dt.second
    # 格式为 HHMMSS
    time_int = hour * 10000 + minute * 100 + second
    return time_int

def timestamp_to_hms_int(timestamp):
    dt = datetime.datetime.fromtimestamp(timestamp)
    hour = dt.hour
    minute = dt.minute
    second = dt.second
    # 格式为 HHMMSS
    time_int = hour * 10000 + minute * 100 + second
    return time_int

def datatime_to_hms_int(dt: datetime.datetime):
    hour = dt.hour
    minute = dt.minute
    second = dt.second
    # 格式为 HHMMSS
    time_int = hour * 10000 + minute * 100 + second
    return time_int

def generate_datetime(timestamp: int, millisecond: bool = True) -> datetime.datetime:
        """生成本地时间"""
        if millisecond:
            dt: datetime.datetime = datetime.datetime.fromtimestamp(timestamp / 1000)
        else:
            dt = datetime.datetime.fromtimestamp(timestamp)
        return dt

def generate_timestamp(datetime: datetime.datetime, millisecond: bool = True) -> int:
        """生成本地时间"""
        timestamp = datetime.timestamp()
        if millisecond:
            timestamp = int(timestamp * 1000)
        else:
            timestamp = int(timestamp * 1000)
        return timestamp


def load_json(filepath: str) -> dict:
    """
    Load data from json file in temp path.
    """
    if filepath.exists():
        with open(filepath, encoding="UTF-8") as f:
            data: dict = json.load(f)
        return data



def save_json(filepath: str, data: dict) -> None:
    """
    Save data into json file in temp path.
    """
    with open(filepath, mode="w+", encoding="UTF-8") as f:
        json.dump(
            data,
            f,
            indent=4,
            ensure_ascii=False
        )


def get_stock_ratio(stock_code):
    """
    根据股票代码获取股票类型。
    简化版：实际应用中需要更精确的方法来确定股票类型。
    """
    if stock_code.startswith(('300', '301')):  # 创业板
        return 0.20
    elif stock_code.startswith('688'):  # 科创板
        return 0.30
    else:  # 主板等其他情况
        return 0.10
