"""
测试股票列表订阅功能
验证客户端订阅股票列表，服务端返回批量数据字典的功能
"""

import time
import threading
import random
from typing import Dict, Any
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_basic_stock_list_subscription():
    """测试基础股票列表订阅功能"""
    print("=== 测试基础股票列表订阅 ===")
    
    from .zmq_pubsub import ZMQPublisher, ZMQSubscriber
    
    # 创建发布者和订阅者
    publisher = ZMQPublisher(port=5555, serialization="msgpack")
    publisher.start()
    
    subscriber = ZMQSubscriber(server_address="localhost", port=5555, serialization="msgpack")
    subscriber.start()
    
    # 测试数据
    stock_codes = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH"]
    subscribed_codes = ["000001.SZ", "600000.SH"]  # 只订阅部分股票
    
    received_data = []
    
    def on_stock_list_data(batch_data: Dict[str, Any]):
        """处理股票列表数据"""
        print(f"收到股票列表数据: {list(batch_data.keys())}")
        for stock_code, data in batch_data.items():
            print(f"  {stock_code}: 价格={data.get('last_price')}, 成交量={data.get('volume')}")
        received_data.append(batch_data)
    
    # 订阅股票列表
    subscriber.subscribe_stock_list(subscribed_codes, on_stock_list_data)
    
    try:
        print(f"订阅股票列表: {subscribed_codes}")
        print("开始发布数据...")
        
        # 发布测试数据
        for round_num in range(5):
            # 生成所有股票的数据
            batch_data = {}
            for stock_code in stock_codes:
                tick_data = {
                    "stock_code": stock_code,
                    "timestamp": int(time.time() * 1000),
                    "last_price": round(10 + random.uniform(-2, 2), 2),
                    "volume": random.randint(100, 10000),
                    "amount": random.randint(1000, 100000),
                    "bid1_price": round(10 + random.uniform(-2, 2), 2),
                    "ask1_price": round(10 + random.uniform(-2, 2), 2)
                }
                batch_data[stock_code] = tick_data
            
            # 发布批量数据
            success = publisher.publish_tick_batch(batch_data)
            print(f"第 {round_num + 1} 轮: 发布 {len(batch_data)} 只股票数据, 成功: {success}")
            
            time.sleep(1)
        
        # 等待接收完成
        time.sleep(2)
        
        # 验证结果
        print(f"\n=== 验证结果 ===")
        print(f"总共接收到 {len(received_data)} 批数据")
        
        for i, data in enumerate(received_data):
            print(f"第 {i+1} 批数据包含股票: {list(data.keys())}")
            # 验证只包含订阅的股票
            for code in data.keys():
                if code not in subscribed_codes:
                    print(f"错误: 收到未订阅的股票数据 {code}")
                else:
                    print(f"正确: 收到订阅的股票数据 {code}")
        
    except KeyboardInterrupt:
        print("测试中断")
    finally:
        subscriber.stop()
        publisher.stop()


def test_high_level_stock_subscription():
    """测试高级股票数据订阅功能"""
    print("\n=== 测试高级股票数据订阅 ===")
    
    from .stock_data_bridge import StockDataPublisher, StockDataSubscriber, StockTickData
    
    # 创建发布者和订阅者
    publisher = StockDataPublisher(port=5557)  # 使用不同端口避免冲突
    publisher.start()
    
    subscriber = StockDataSubscriber(server_address="localhost", port=5557)
    subscriber.start()
    
    # 测试数据
    stock_codes = ["000001.SZ", "000002.SZ", "600000.SH", "600036.SH", "000858.SZ"]
    subscribed_codes = ["000001.SZ", "000002.SZ", "600000.SH"]  # 订阅部分股票
    
    received_batches = []
    
    def on_stock_list_handler(tick_dict: Dict[str, StockTickData]):
        """处理股票列表数据"""
        print(f"收到股票列表 (StockTickData): {list(tick_dict.keys())}")
        for stock_code, tick_data in tick_dict.items():
            print(f"  {stock_code}: 价格={tick_data.last_price}, 成交量={tick_data.volume}")
        received_batches.append(tick_dict)
    
    # 订阅股票列表
    subscriber.subscribe_stock_list(subscribed_codes, on_stock_list_handler)
    
    try:
        print(f"订阅股票列表: {subscribed_codes}")
        print("开始发布股票数据...")
        
        # 发布测试数据
        for round_num in range(3):
            # 生成市场快照数据
            market_data = {}
            for stock_code in stock_codes:
                tick_data = {
                    "stock_code": stock_code,
                    "timestamp": int(time.time() * 1000),
                    "last_price": round(15 + random.uniform(-3, 3), 2),
                    "volume": random.randint(1000, 50000),
                    "amount": random.randint(10000, 500000),
                    "open": round(15 + random.uniform(-2, 2), 2),
                    "high": round(15 + random.uniform(0, 4), 2),
                    "low": round(15 + random.uniform(-4, 0), 2),
                    "pre_close": 15.0,
                    "bid1_price": round(15 + random.uniform(-3, 3), 2),
                    "bid1_vol": random.randint(100, 1000),
                    "ask1_price": round(15 + random.uniform(-3, 3), 2),
                    "ask1_vol": random.randint(100, 1000)
                }
                market_data[stock_code] = tick_data
            
            # 发布市场快照
            success = publisher.publish_market_snapshot(market_data)
            print(f"第 {round_num + 1} 轮: 发布市场快照 {len(market_data)} 只股票, 成功: {success}")
            
            time.sleep(1.5)
        
        # 等待接收完成
        time.sleep(2)
        
        # 验证结果
        print(f"\n=== 验证高级订阅结果 ===")
        print(f"总共接收到 {len(received_batches)} 批数据")
        
        for i, tick_dict in enumerate(received_batches):
            print(f"第 {i+1} 批数据:")
            for stock_code, tick_data in tick_dict.items():
                print(f"  {stock_code}: {type(tick_data).__name__} - 价格={tick_data.last_price}")
                # 验证是StockTickData对象
                if not isinstance(tick_data, StockTickData):
                    print(f"错误: {stock_code} 不是StockTickData对象")
                else:
                    print(f"正确: {stock_code} 是StockTickData对象")
        
    except KeyboardInterrupt:
        print("测试中断")
    finally:
        subscriber.stop()
        publisher.stop()


def test_individual_aggregation():
    """测试单个股票聚合订阅功能"""
    print("\n=== 测试单个股票聚合订阅 ===")
    
    from .stock_data_bridge import StockDataPublisher, StockDataSubscriber, StockTickData
    
    # 创建发布者和订阅者
    publisher = StockDataPublisher(port=5558)  # 使用不同端口
    publisher.start()
    
    subscriber = StockDataSubscriber(server_address="localhost", port=5558)
    subscriber.start()
    
    # 测试数据
    subscribed_codes = ["000001.SZ", "000002.SZ"]
    
    received_aggregated = []
    
    def on_aggregated_data(tick_dict: Dict[str, StockTickData]):
        """处理聚合的股票数据"""
        print(f"收到聚合数据: {list(tick_dict.keys())}")
        for stock_code, tick_data in tick_dict.items():
            print(f"  {stock_code}: 价格={tick_data.last_price}")
        received_aggregated.append(tick_dict)
    
    # 使用单个股票聚合订阅
    subscriber.subscribe_stock_list_individual(subscribed_codes, on_aggregated_data)
    
    try:
        print(f"使用单个股票聚合订阅: {subscribed_codes}")
        print("开始逐个发布股票数据...")
        
        # 逐个发布股票数据
        for round_num in range(3):
            print(f"\n第 {round_num + 1} 轮发布:")
            
            for stock_code in subscribed_codes:
                tick_data = {
                    "stock_code": stock_code,
                    "timestamp": int(time.time() * 1000),
                    "last_price": round(20 + random.uniform(-5, 5), 2),
                    "volume": random.randint(500, 5000),
                    "amount": random.randint(5000, 50000),
                    "pre_close": 20.0
                }
                
                # 发布单个股票数据
                success = publisher.publish_tick(tick_data)
                print(f"  发布 {stock_code}: {success}")
                
                time.sleep(0.2)  # 短暂延迟
            
            time.sleep(1)
        
        # 等待聚合完成
        time.sleep(2)
        
        # 验证结果
        print(f"\n=== 验证聚合结果 ===")
        print(f"总共接收到 {len(received_aggregated)} 批聚合数据")
        
        for i, tick_dict in enumerate(received_aggregated):
            print(f"第 {i+1} 批聚合数据包含: {list(tick_dict.keys())}")
            if len(tick_dict) == len(subscribed_codes):
                print("正确: 聚合了所有订阅的股票")
            else:
                print(f"警告: 聚合数据不完整，期望 {len(subscribed_codes)} 只，实际 {len(tick_dict)} 只")
        
    except KeyboardInterrupt:
        print("测试中断")
    finally:
        subscriber.stop()
        publisher.stop()


def run_all_tests():
    """运行所有测试"""
    print("开始运行股票列表订阅功能测试...\n")
    
    try:
        # 测试1: 基础ZMQ股票列表订阅
        test_basic_stock_list_subscription()
        
        time.sleep(2)  # 间隔
        
        # 测试2: 高级股票数据订阅
        test_high_level_stock_subscription()
        
        time.sleep(2)  # 间隔
        
        # 测试3: 单个股票聚合订阅
        test_individual_aggregation()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("python test_stock_list_subscription.py basic      # 基础ZMQ测试")
        print("python test_stock_list_subscription.py highlevel  # 高级封装测试")
        print("python test_stock_list_subscription.py individual # 单个聚合测试")
        print("python test_stock_list_subscription.py all        # 运行所有测试")
        sys.exit(1)
    
    mode = sys.argv[1]
    
    if mode == "basic":
        test_basic_stock_list_subscription()
    elif mode == "highlevel":
        test_high_level_stock_subscription()
    elif mode == "individual":
        test_individual_aggregation()
    elif mode == "all":
        run_all_tests()
    else:
        print(f"未知测试模式: {mode}")
        sys.exit(1)
